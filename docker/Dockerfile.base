# Income FE 项目基础镜像
# 基于nginx alpine版本，添加项目特定的配置和优化

FROM nginx:1.25-alpine

# 镜像元信息
LABEL maintainer="income-fe-team"
LABEL version="1.0.0"
LABEL description="Income FE project custom nginx base image"

# 安装必要的工具
RUN apk add --no-cache \
    curl \
    tzdata \
    ca-certificates

# 设置时区为中国时区
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 创建必要的目录
RUN mkdir -p /var/log/nginx /var/cache/nginx /usr/share/nginx/html

# 优化nginx配置
RUN sed -i 's/worker_processes  1/worker_processes  auto/' /etc/nginx/nginx.conf && \
    sed -i 's/worker_connections  1024/worker_connections  2048/' /etc/nginx/nginx.conf

# 添加自定义nginx配置
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# 设置正确的权限
RUN chown -R nginx:nginx /var/cache/nginx /var/log/nginx /usr/share/nginx/html

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 暴露端口
EXPOSE 80

# 启动命令
CMD ["nginx", "-g", "daemon off;"]
