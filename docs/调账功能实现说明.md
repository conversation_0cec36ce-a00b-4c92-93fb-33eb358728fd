# 调账功能实现说明

## 功能概述
在权责调账页面中实现了完整的调账功能，用户可以对权责记录进行调账操作。

## 实现的功能

### 1. 调账弹框组件 (AdjustAccountDialog.vue)
- **位置**: `src/views/account/AdjustAccountDialog.vue`
- **功能**: 提供调账操作的用户界面
- **特性**:
  - Apple Design System 风格的弹框设计
  - 表单验证和错误提示
  - 响应式布局
  - 加载状态指示

### 2. 调账API接口
- **接口地址**: `/charge-adjust/adjust-account`
- **请求方法**: POST
- **请求数据结构**:
  ```typescript
  {
    "batch_no": "当前权责的批次号",
    "order_no": "当前权责的订单编号", 
    "sub_order_no": "当前权责的子订单编号",
    "charge_month": "权责月份(YYMM格式)",
    "adjust_month": "调账月份(当前权责的charge_month)",
    "adjust_amount": "调账金额(InputNumber输入)",
    "adjust_tax": "调账税率(当前权责的tax)",
    "adjust_reason_class": "调账原因分类(下拉框选择)",
    "adjust_reason": "调账原因(文本输入)",
    "adjust_charge_detail_id": "当前权责的id"
  }
  ```

### 3. 表单字段说明

#### 只读字段 (默认为当前权责的值)
- **批次号**: 显示当前权责的 `batch_no`
- **订单编号**: 显示当前权责的 `order_no`
- **子订单编号**: 显示当前权责的 `sub_order_no`
- **调账月份**: 默认为当前权责的 `charge_month`
- **调账税率**: 默认为当前权责的 `tax`

#### 可编辑字段
- **权责月份**: 日历控件，格式为YYMM
- **调账金额**: InputNumber组件，支持货币格式输入
- **调账原因分类**: Select下拉框，调用 `getStaticDataList` API，参数 `data_type = adjust_reason_class`
- **调账原因**: Textarea文本输入框

### 4. 数据验证
- 调账金额必须大于0
- 调账原因分类必须选择
- 调账原因必须填写
- 日期字段不能为空

### 5. 技术实现

#### 类型定义更新 (src/types/adjustDetail.ts)
```typescript
// 新增调账请求数据类型
export interface AdjustAccountRequest {
  batch_no: string;
  order_no: string;
  sub_order_no: string;
  charge_month: string;
  adjust_month: string;
  adjust_amount: number;
  adjust_tax: number;
  adjust_reason_class: string;
  adjust_reason: string;
  adjust_charge_detail_id: number;
}

// 更新权责调账项目类型，添加税率字段
export interface ChargeAdjustItem {
  // ... 其他字段
  tax: number; // 新增税率字段
}
```

#### API服务更新 (src/services/adjustDetail.ts)
```typescript
// 新增调账操作API
export const adjustAccount = async (
  data: AdjustAccountRequest
): Promise<ApiResponse<any>> => {
  const response = await api.post("/charge-adjust/adjust-account", data);
  return response.data;
};
```

#### 主页面集成 (src/views/account/Adjust.vue)
- 导入调账弹框组件
- 添加弹框状态管理
- 更新调账按钮点击事件
- 实现调账成功后的数据刷新

### 6. 用户操作流程
1. 在权责调账Tab中查看权责列表
2. 点击某条记录的"调账"按钮
3. 弹出调账操作弹框
4. 查看只读的权责信息
5. 填写调账金额、选择调账原因分类、输入调账原因
6. 可选择修改权责月份
7. 点击"确认调账"按钮提交
8. 系统验证数据并调用API
9. 操作成功后显示成功提示并关闭弹框
10. 自动刷新权责调账列表和调账记录列表

### 7. 界面特性
- **Apple Design System风格**: 圆角、渐变、阴影等视觉效果
- **响应式设计**: 支持不同屏幕尺寸
- **表单验证**: 实时验证和错误提示
- **加载状态**: 提交时显示加载指示器
- **用户体验**: 平滑的动画和交互效果

### 8. 错误处理
- 网络请求失败时显示错误提示
- 表单验证失败时高亮错误字段
- API返回错误时显示具体错误信息
- 加载静态数据失败时的错误处理

### 9. 后续扩展建议
1. **批量调账**: 支持选择多条记录进行批量调账
2. **调账历史**: 显示某条权责的历史调账记录
3. **调账审批**: 添加调账审批流程
4. **导出功能**: 支持导出调账记录
5. **权限控制**: 根据用户角色控制调账权限

## 注意事项
1. 确保后端API `/charge-adjust/adjust-account` 已实现
2. 确保 `getStaticDataList` API 支持 `adjust_reason_class` 类型
3. 权责调账列表的数据结构需要包含 `tax` 字段
4. 日期格式统一使用YYMM格式
5. 调账成功后会自动刷新相关列表数据
