{"name": "vite-vue-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@primeuix/themes": "^1.0.2", "@tailwindcss/vite": "^4.1.8", "axios": "^1.8.4", "material-icons": "^1.13.14", "pinia": "^2.1.7", "primeicons": "^6.0.1", "primevue": "^4.3.3", "tailwindcss": "^4.1.8", "tailwindcss-primeui": "^0.6.1", "vue": "^3.4.38", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.3", "typescript": "^5.5.3", "vite": "^6.2.4", "vue-tsc": "^2.2.10"}}