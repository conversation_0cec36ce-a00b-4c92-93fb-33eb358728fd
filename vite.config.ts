import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
  ],
  server: {
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        rewrite: (path) => path
      }
    }
  },
  // resolve: {
  //   alias: {
  //     '@': path.resolve(__dirname, 'src')
  //   }
  // },
  build: {
    outDir: 'dist',
    // assetsDir: 'static',
    // emptyOutDir: true, // 每次打包清空输出目录
    // target: 'es2015', // 构建目标
    // rollupOptions: {
    //   input: {
    //     main: './src/main.ts',
    //   },
    //   output: {
    //     chunkFileNames: 'js/[name].[hash].js',
    //     entryFileNames: 'js/[name].[hash].js',
    //     assetFileNames: 'static/[name].[hash].[ext]',
    //   },
    // },
    // minify: true,
    sourcemap: false,
    // base: '/your-cdn-path/', // 如需 CDN 路径可取消注释
  },
});