import { computed } from "vue";
import { useRoute } from "vue-router";
import { useUserStore } from "../stores/user";
import { checkAuth } from "../services/auth";

/**
 * 权限管理组合式函数
 * 提供统一的权限判断逻辑，解决页面刷新时权限获取失败的问题
 */
export function usePermission() {
  const route = useRoute();
  const userStore = useUserStore();

  /**
   * 递归查找菜单中指定路径的菜单项
   * @param menus 菜单数组
   * @param path 路径
   * @returns 找到的菜单项或null
   */
  const findMenuByPath = (menus: any[], path: string): any => {
    for (const menu of menus) {
      if (menu.router === path) {
        return menu;
      }
      if (menu.children) {
        const found = findMenuByPath(menu.children, path);
        if (found) return found;
      }
    }
    return null;
  };

  /**
   * 计算当前页面的操作权限
   * 优先从用户store中的菜单获取权限，回退到路由meta
   */
  const hasOperationPermission = computed(() => {
    // 首先尝试从用户store中的菜单获取权限
    if (userStore.user?.menus) {
      const currentPath = route.path;
      const currentMenu = findMenuByPath(userStore.user.menus, currentPath);
      if (currentMenu) {
        return currentMenu.operation ?? false;
      }
    }

    // 回退到路由meta中的权限信息
    const currentRoute = route.matched[route.matched.length - 1];
    return currentRoute?.meta?.operation ?? false;
  });

  /**
   * 初始化用户信息（如果store中没有）
   * 解决页面刷新时用户信息丢失的问题
   */
  const initializeUserInfo = async () => {
    if (!userStore.user) {
      try {
        const response = await checkAuth();
        userStore.setUser(response.data);
      } catch (error) {
        console.error("Failed to initialize user info:", error);
        throw error;
      }
    }
  };

  /**
   * 检查指定路径的操作权限
   * @param path 路径，如果不提供则使用当前路径
   * @returns 是否有操作权限
   */
  const checkOperationPermission = (path?: string): boolean => {
    const targetPath = path || route.path;
    
    if (userStore.user?.menus) {
      const menu = findMenuByPath(userStore.user.menus, targetPath);
      if (menu) {
        return menu.operation ?? false;
      }
    }

    // 如果没有指定路径，回退到当前路由meta
    if (!path) {
      const currentRoute = route.matched[route.matched.length - 1];
      return Boolean(currentRoute?.meta?.operation) ?? false;
    }

    return false;
  };

  return {
    hasOperationPermission,
    initializeUserInfo,
    checkOperationPermission,
    findMenuByPath,
  };
}
