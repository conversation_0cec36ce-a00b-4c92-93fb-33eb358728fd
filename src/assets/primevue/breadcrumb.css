.p-breadcrumb {
    @apply bg-surface-0 dark:bg-surface-900 p-4 overflow-x-auto
}

.p-breadcrumb-list {
    @apply m-0 p-0 list-none flex items-center flex-nowrap gap-2
}

.p-breadcrumb-separator {
    @apply flex items-center text-surface-400 dark:text-surface-500
}

.p-breadcrumb-separator-icon:dir(rtl) {
    @apply rotate-180
}

.p-breadcrumb::-webkit-scrollbar {
    @apply hidden
}

.p-breadcrumb-item-link {
    @apply no-underline flex items-center gap-2 transition-colors duration-200 rounded-md
        text-surface-500 dark:text-surface-400
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary
}

.p-breadcrumb-item-link:hover .p-breadcrumb-item-label {
    @apply text-surface-700 dark:text-surface-0
}

.p-breadcrumb-item-label {
    @apply transition-colors duration-200
}

.p-breadcrumb-item-icon {
    @apply text-surface-400 dark:text-surface-500 transition-colors duration-200
}

.p-breadcrumb-item-link:hover .p-breadcrumb-item-icon {
    @apply text-surface-500 dark:text-surface-400
}
