@import './chip';
@import './tree';

.p-treeselect {
    @apply inline-flex cursor-pointer relative select-none rounded-md
        bg-surface-0 dark:bg-surface-950
        border border-surface-300 dark:border-surface-700
        shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]
        transition-colors duration-200
}

.p-treeselect:not(.p-disabled):hover {
    @apply border-surface-400 dark:border-surface-600
}

.p-treeselect:not(.p-disabled).p-focus {
    @apply border-primary
}

.p-treeselect.p-variant-filled {
    @apply bg-surface-50 dark:bg-surface-800
}

.p-treeselect.p-invalid {
    @apply border-red-400 dark:border-red-300
}

.p-treeselect.p-disabled {
    @apply bg-surface-200 text-surface-500 dark:bg-surface-700 dark:text-surface-400 opacity-100 cursor-default
}

.p-treeselect-dropdown {
    @apply flex items-center justify-center shrink-0 bg-transparent
        text-surface-500 dark:text-surface-400 w-10 rounded-e-md
}

.p-treeselect-clear-icon {
    @apply absolute top-1/2 -mt-2 text-surface-500 dark:text-surface-400 end-10
}

.p-treeselect-label-container {
    @apply overflow-hidden flex-auto
}

.p-treeselect-label {
    @apply flex items-center gap-1 whitespace-nowrap text-ellipsis px-3 py-2 text-surface-700 dark:text-surface-0
}

.p-treeselect-label.p-placeholder {
    @apply text-surface-500 dark:text-surface-400
}

.p-treeselect.p-disabled .p-treeselect-label {
    @apply text-surface-500 dark:text-surface-400
}

.p-treeselect-label-empty {
    @apply overflow-hidden invisible
}

.p-treeselect .p-treeselect-overlay {
    @apply min-w-full
}

.p-treeselect-overlay {
    @apply absolute top-0 left-0 rounded-md
        bg-surface-0 dark:bg-surface-900
        border border-surface-200 dark:border-surface-700
        text-surface-700 dark:text-surface-0
        shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]
}


.p-treeselect-tree-container {
    @apply overflow-auto
}

.p-treeselect-empty-message {
    @apply px-3 py-2
}

.p-treeselect-fluid {
    @apply flex
}

.p-treeselect-overlay .p-tree {
    @apply p-1
}

.p-treeselect-overlay .p-tree-loading {
    @apply min-h-12
}

.p-treeselect-label .p-chip {
    @apply pt-1 pb-1 rounded-sm
}

.p-treeselect-label:has(.p-chip) {
    @apply py-1 px-1
}

.p-treeselect-sm .p-treeselect-label {
    @apply text-sm px-[0.625rem] py-[0.375rem]
}

.p-treeselect-sm .p-treeselect-dropdown .p-icon {
    @apply text-sm w-[0.875rem] h-[0.875rem]
}

.p-treeselect-lg .p-treeselect-label {
    @apply text-lg px-[0.875rem] py-[0.625rem]
}

.p-treeselect-lg .p-treeselect-dropdown .p-icon {
    @apply text-lg w-[1.125rem] h-[1.125rem]
}
