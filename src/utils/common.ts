export const initNumber = async (prefix: string) => {
  // 生成customer_num 格式为KH-%Y%m%d-5位随机字符串（数字小写字母大写字母）
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // 补零
  const day = String(today.getDate()).padStart(2, "0");
  const dateStr = `${year}${month}${day}`; // 如 "20240521"

  const chars =
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  let randomStr = "";
  for (let i = 0; i < 5; i++) {
    randomStr += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return `${prefix}-${dateStr}-${randomStr}`;
};

export const formatDateTime = (timestamp: string | number) => {
  // 如果timestamp是null或者undefined，返回空字符串
  if (!timestamp) {
    return "";
  }
  return new Date(Number(timestamp) * 1000).toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

export const isoFormatDatetime = (
  date: Date | string | null | undefined
): string => {
  if (!date) return "";
  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  const seconds = String(d.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

export const isoFormatDate = (
  date: Date | string | null | undefined
): string => {
  if (!date) return "";
  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

// 将Date对象转换为yyyymm格式字符串
export const isoFormatYYmm = (date: Date | null): string | undefined => {
  if (!date) return undefined;
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `${year}${month}`;
};

// 格式化金额
export const formatCurrency = (amount: string | number | null | undefined, currencyType?: string) => {
  if (!amount && amount !== 0) return "未设置";
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numericAmount)) return "未设置";
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: currencyType || "CNY",
    minimumFractionDigits: 2,
  }).format(numericAmount);
};