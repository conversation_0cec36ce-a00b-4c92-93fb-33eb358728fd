import { type Ref } from "vue";
import { useToast } from "primevue/usetoast";
import { getStaticDataList } from "../services/public";

/**
 * 通用的静态数据选项加载器
 * @param word - 静态数据的关键字
 * @param optionsRef - 存储选项的响应式引用
 * @param errorMessage - 错误提示信息
 */
export const loadStaticOptions = async (
  word: string,
  optionsRef: Ref<{ label: string; value: string }[]>,
  errorMessage: string
) => {
  const toast = useToast();
  
  try {
    const response = await getStaticDataList({ word });
    optionsRef.value = response.data.map((item) => ({
      label: item.data_value,
      value: item.data_value,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: errorMessage,
      life: 3000,
    });
  }
};

/**
 * 创建静态选项加载器的工厂函数
 * @param word - 静态数据的关键字
 * @param errorMessage - 错误提示信息
 * @returns 返回一个加载函数
 */
export const createStaticOptionsLoader = (word: string, errorMessage: string) => {
  return (optionsRef: Ref<{ label: string; value: string }[]>) => 
    loadStaticOptions(word, optionsRef, errorMessage);
};

/**
 * 预定义的常用选项加载器
 */
export const optionLoaders = {
  businessProductType: (optionsRef: Ref<{ label: string; value: string }[]>) =>
    loadStaticOptions("business_product_type", optionsRef, "加载业务产品类型选项失败"),
    
  incomeType: (optionsRef: Ref<{ label: string; value: string }[]>) =>
    loadStaticOptions("income_type", optionsRef, "加载收入分类选项失败"),
    
  currencyType: (optionsRef: Ref<{ label: string; value: string }[]>) =>
    loadStaticOptions("currency_type", optionsRef, "加载货币类型选项失败"),
    
  taxRateType: (optionsRef: Ref<{ label: string; value: string }[]>) =>
    loadStaticOptions("charge_tax_type", optionsRef, "加载税率类型选项失败"),
    
  signContractEntity: (optionsRef: Ref<{ label: string; value: string }[]>) =>
    loadStaticOptions("sign_contract_entity", optionsRef, "加载签约主体选项失败"),

  adjustReasonClass: (optionsRef: Ref<{ label: string; value: string }[]>) =>
    loadStaticOptions("adjust_reason_class", optionsRef, "加载调账原因分类选项失败"),
};
