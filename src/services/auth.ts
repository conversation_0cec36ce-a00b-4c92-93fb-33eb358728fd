import { ApiResponse } from "../types/api";
import api from "./api";
import { startAuth<PERSON>heck, stopAuth<PERSON>heck } from "./api";

export interface Menu {
  id: number;
  name: string;
  identify: string;
  icon: string;
  router?: string;
  operation?: boolean;
  children: Menu[] | null;
}

export interface User {
  username: string;
  email: string;
  menus: Menu[];
}

export interface LogoutResponse {
  message: string;
}

export const login = async (
  username: string,
  password: string
): Promise<ApiResponse<User>> => {
  const response = await api.post<ApiResponse<User>>(
    "/login",
    { username, password },
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
  startAuthCheck();
  return response.data;
};

export const logout = async (): Promise<ApiResponse<LogoutResponse>> => {
  const response = await api.post("/logout");
  stopAuthCheck();
  return response.data;
};

export const checkAuth = async (): Promise<ApiResponse<User>> => {
  const response = await api.get<ApiResponse<User>>("/auth/check");
  return response.data;
};
