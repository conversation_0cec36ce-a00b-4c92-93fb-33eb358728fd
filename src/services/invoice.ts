import { ApiListResponse, ApiResponse } from "../types/api";
import type {
  InvoiceInfoItem,
  InvoiceFormData,
  InvoiceSearchParams,
} from "../types/invoice";
import api from "./api";

export const getInvoiceList = async (
  params: InvoiceSearchParams
): Promise<ApiListResponse<InvoiceInfoItem[]>> => {
  const response = await api.get("/contract/invoices-info", { params });
  return response.data;
};

export const createInvoice = async (data: InvoiceFormData): Promise<any> => {
  const response = await api.post("/contract/invoices-info", data);
  return response.data;
};

export const updateInvoice = async (
  id: number,
  data: InvoiceFormData
): Promise<any> => {
  const response = await api.put(`/contract/invoices-info/${id}`, data);
  return response.data;
};

export const getInvoiceDetail = async (
  id: number
): Promise<ApiResponse<InvoiceInfoItem>> => {
  const response = await api.get<ApiResponse<InvoiceInfoItem>>(
    `/contract/invoices-info/${id}`
  );
  return response.data;
};
