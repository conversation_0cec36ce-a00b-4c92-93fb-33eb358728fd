import { ApiListResponse, ApiResponse } from "../types/api";
import {
  BankStatementItem,
  BankStatementUpdateFormData,
  BankStatementParams,
  ReceiveStatementItem,
  RecognitionDetailItem,
  RecognitionDetailParams,
} from "../types/bankStatement";
import api from "./api";

// 获取银行流水列表
export const getBankStatements = async (
  params: BankStatementParams
): Promise<ApiListResponse<BankStatementItem[]>> => {
  const response = await api.get("/bank-statements", { params });
  return response.data;
};

// 更新银行流水
export const updateBankStatement = async (
  id: number,
  data: BankStatementUpdateFormData
): Promise<ApiResponse<any>> => {
  const response = await api.put(`/bank-statements/${id}`, data);
  return response.data;
};

// 获取已认款列表
export const getReceiveStatements = async (
  bankStatementId: number,
  params: { page?: number; pageSize?: number }
): Promise<ApiListResponse<ReceiveStatementItem[]>> => {
  const response = await api.get(`/bank-statements/${bankStatementId}/receive-statements`, { params });
  return response.data;
};

// 获取待认款列表
export const getRecognitionDetails = async (
  bankStatementId: number,
  params: RecognitionDetailParams
): Promise<ApiListResponse<RecognitionDetailItem[]>> => {
  const response = await api.get(`/bank-statements/${bankStatementId}/recognition-details`, { params });
  return response.data;
};

// 获取单个银行流水详情
export const getBankStatementDetail = async (
  id: number
): Promise<ApiResponse<BankStatementItem>> => {
  const response = await api.get(`/bank-statements/${id}`);
  return response.data;
};
