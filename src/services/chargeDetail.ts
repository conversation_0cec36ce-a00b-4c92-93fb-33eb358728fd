import { ApiListResponse } from "../types/api";
import { ChargeDetailItem, ChargeDetailParams } from "../types/chargeDetail";
import api from "./api";

// 获取收入权责列表
export const getChargeDetails = async (
  params: ChargeDetailParams
): Promise<ApiListResponse<ChargeDetailItem[]>> => {
  const response = await api.get("/charge-details", { params });
  return response.data;
};

// 导出收入权责列表
export const exportChargeDetails = async (params: ChargeDetailParams): Promise<{
  blob: Blob;
  filename: string;
}> => {
  // 过滤空值
  const filteredParams = { ...params };
  Object.keys(filteredParams).forEach((key) => {
    if (filteredParams[key] === "" || filteredParams[key] === null) {
      delete filteredParams[key];
    }
  });

  const response = await api.get("/charge-details/export", {
    params: filteredParams,
    responseType: "blob",
  });

  // 从响应头中获取文件名
  const contentDisposition = response.headers["content-disposition"];
  let filename = "收入权责明细.xlsx";

  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
    if (filenameMatch && filenameMatch[1]) {
      try {
        filename = decodeURIComponent(filenameMatch[1]);
      } catch (error) {
        console.error("Error decoding filename:", error);
      }
    }
  }

  return {
    blob: response.data,
    filename: filename,
  };
};
