import { DepartmentResponse } from "../types/department";
import api from "./api";

export const getDepartments = async (): Promise<DepartmentResponse> => {
  const response = await api.get("/departments");
  return response.data;
};

interface DepartmentParams {
  department_name: string;
  parent_id?: number | null;
}

export const createDepartment = async (params: DepartmentParams) => {
  const response = await api.post("/departments", params, {
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response.data;
};

export const updateDepartment = async (params: DepartmentParams, id: number) => {
  const response = await api.put(`/departments/${id}`, params, {
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response.data;
};
