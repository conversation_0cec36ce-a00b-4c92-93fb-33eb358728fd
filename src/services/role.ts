import { ApiListResponse, ApiResponse } from "../types/api";
import { Role } from "../types/role";
import api from "./api";

export interface RoleSimple {
  id: number;
  role_name: string;
}

export const getRoleSimple = async (): Promise<ApiResponse<RoleSimple[]>> => {
  const response = await api.get<ApiResponse<RoleSimple[]>>(
    "/roles/simple-list"
  );
  return response.data;
};

interface RoleParams {
  role_name: string;
  role_code: string;
  menus: {
    menu_id: number;
    operation: boolean | null;
  }[];
}

export const createRole = async (params: RoleParams) => {
  const response = await api.post("/roles", params, {
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response.data;
};

export interface RoleQueryParams {
  page?: number;
  pageSize?: number;
  word?: string;
}

export const getRoles = async (
  params: RoleQueryParams
): Promise<ApiListResponse<Role[]>> => {
  const response = await api.get("/roles", { params });
  return response.data;
};

export const updateRole = async (params: RoleParams, id: number) => {
  const response = await api.put(`/roles/${id}`, params, {
    headers: {
      "Content-Type": "application/json",
    },
  });
  return response.data;
};
