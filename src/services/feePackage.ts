import { ApiListResponse, ApiResponse } from "../types/api";
import {
  FeePackageItem,
  FeePackageSimpleItem,
  FeeInstanceItem,
  FeeInstanceLevelItem,
  FeeTemplateSimpleItem,
  FeeInstanceFormData,
  FeeInstanceLevelFormData,
} from "../types/feePackage";
import api from "./api";

// 费用套餐表单数据接口
export interface FeePackageFormData {
  package_name: string;
  remark?: string;
}

export interface FeePackageParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
}

// 获取费用套餐列表
export const getFeePackages = async (
  params: FeePackageParams
): Promise<ApiListResponse<FeePackageItem[]>> => {
  const response = await api.get("/fee-packages", { params });
  return response.data;
};

// 获取套餐费用Simple列表
export const getFeePackagesSimpleList = async (): Promise<
  ApiResponse<FeePackageSimpleItem[]>
> => {
  const response = await api.get("/fee-packages/simple-list");
  return response.data;
};

// 创建费用套餐
export const createFeePackage = async (
  data: FeePackageFormData
): Promise<ApiResponse<FeePackageItem>> => {
  const response = await api.post("/fee-packages", data);
  return response.data;
};

// 更新费用套餐
export const updateFeePackage = async (
  id: number,
  data: FeePackageFormData
): Promise<ApiResponse<FeePackageItem>> => {
  const response = await api.put(`/fee-packages/${id}`, data);
  return response.data;
};

export interface FeeInstanceParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
}

// 获取费用实例列表
export const getFeeInstances = async (
  packageId: number,
  params: FeeInstanceParams
): Promise<ApiListResponse<FeeInstanceItem[]>> => {
  const response = await api.get(`/fee-packages/${packageId}/fee-instances`, {
    params,
  });
  return response.data;
};

// 创建费用实例
export const createFeeInstance = async (
  packageId: number,
  data: FeeInstanceFormData
): Promise<ApiResponse<FeeInstanceItem>> => {
  const response = await api.post(
    `/fee-packages/${packageId}/fee-instances`,
    data
  );
  return response.data;
};

// 更新费用实例
export const updateFeeInstance = async (
  packageId: number,
  instanceId: number,
  data: FeeInstanceFormData
): Promise<ApiResponse<FeeInstanceItem>> => {
  const response = await api.put(
    `/fee-packages/${packageId}/fee-instances/${instanceId}`,
    data
  );
  return response.data;
};

// 获取费用模板简单列表
export const getFeeTemplatesSimpleList = async (): Promise<
  ApiResponse<FeeTemplateSimpleItem[]>
> => {
  const response = await api.get("/fee-templates/simple-list");
  return response.data;
};

// 获取费用实例等级列表
export const getFeeInstanceLevels = async (
  instanceId: number,
): Promise<ApiResponse<FeeInstanceLevelItem[]>> => {
  const response = await api.get(
    `/fee-instances/${instanceId}/fee-instance-levels`,
  );
  return response.data;
};

// 创建费用实例等级
export const createFeeInstanceLevel = async (
  instanceId: number,
  data: FeeInstanceLevelFormData
): Promise<ApiResponse<FeeInstanceLevelItem>> => {
  const response = await api.post(
    `/fee-instances/${instanceId}/fee-instance-levels`,
    data
  );
  return response.data;
};

// 更新费用实例等级
export const updateFeeInstanceLevel = async (
  instanceId: number,
  levelId: number,
  data: FeeInstanceLevelFormData
): Promise<ApiResponse<FeeInstanceLevelItem>> => {
  const response = await api.put(
    `/fee-instances/${instanceId}/fee-instance-levels/${levelId}`,
    data
  );
  return response.data;
};
