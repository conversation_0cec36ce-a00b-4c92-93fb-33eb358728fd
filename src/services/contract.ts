import { ApiListResponse, ApiResponse } from "../types/api";
import {
  ContractFormData,
  ContractItem,
  ContractSimpleInfo,
} from "../types/contract";
import { getCustomersSimpleList } from "./customer";
import api from "./api";

export interface ContractsParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
  main_customer_num?: string;
  [key: string]: any;
}

export const getContracts = async (
  params: ContractsParams
): Promise<ApiListResponse<ContractItem[]>> => {
  const response = await api.get("/customers/contracts", { params });
  return response.data;
};

export const createContract = async (data: ContractFormData): Promise<any> => {
  const response = await api.post("/customers/contracts", data);
  return response.data;
};

export const updateContract = async (
  id: string,
  data: Partial<ContractFormData>
): Promise<any> => {
  const response = await api.put(`/customers/contracts/${id}`, data);
  return response.data;
};

export const getContractDetail = async (
  id: string
): Promise<ApiResponse<ContractItem>> => {
  const response = await api.get<ApiResponse<ContractItem>>(
    `/customers/contracts/${id}`
  );
  return response.data;
};

// 导出客户简单列表方法供合同页面使用
export { getCustomersSimpleList };

// 获取合同信息简单列表（用于下拉框）
export const getContractsSimpleList = async (): Promise<
  ApiResponse<ContractSimpleInfo[]>
> => {
  const response = await api.get("/customers/contracts/simple-list");
  return response.data;
};

export interface AccountSeqSimpleInfo {
  id: number;
  customer_invoice_name: string;
  account_seq: string;
}

// 获取分账序号简单列表
export const getInvocieInfoSimpleList = async (
  contract_num: string
): Promise<ApiResponse<AccountSeqSimpleInfo[]>> => {
  const response = await api.get(`/contract/invoices-info/simple-list`, {
    params: {
      contract_num,
    },
  });
  return response.data;
};
