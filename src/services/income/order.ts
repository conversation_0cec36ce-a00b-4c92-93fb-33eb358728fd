import { ApiListResponse, ApiResponse } from "../../types/api";
import { OrderItem, OrderFormData } from "../../types/order";
import api from "../api";
import { getContractsSimpleList } from "../contract";

export interface OrdersParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
  [key: string]: any;
}

export const getOrders = async (
  params: OrdersParams
): Promise<ApiListResponse<OrderItem[]>> => {
  const response = await api.get("/orders-info", { params });
  return response.data;
};

export const createOrder = async (data: OrderFormData): Promise<any> => {
  const response = await api.post("/orders-info", data);
  return response.data;
};

export const updateOrder = async (
  id: number,
  data: Partial<OrderFormData>
): Promise<any> => {
  const response = await api.put(`/orders-info/${id}`, data);
  return response.data;
};

// 变更订单
export const changeOrder = async (
  id: number,
  data: OrderFormData
): Promise<any> => {
  const response = await api.post(`/orders-info/${id}/change-order`, data);
  return response.data;
};

// 新建子订单
export const createSubOrder = async (
  id: number,
  data: OrderFormData
): Promise<any> => {
  const response = await api.post(`/orders-info/${id}/new-sub-order`, data);
  return response.data;
};

// 续约订单
export const createRenewalOrder = async (
  id: number,
  data: OrderFormData
): Promise<any> => {
  const response = await api.post(`/orders-info/${id}/renewal-order`, data);
  return response.data;
};

export const getOrderDetail = async (
  id: number
): Promise<ApiResponse<OrderItem>> => {
  const response = await api.get(`/orders-info/${id}`);
  return response.data;
};

export interface ChangeServiceStatusData {
  service_status: string;
  finished_remark?: string;
  new_build_start_time?: string;
  order_remark?: string;
  job_status?: string;
  product_scheme?: string;
  remove_required_finished_date?: string;
  remove_build_start_time?: string;
}

export const changeServiceStatus = async (
  orderId: number,
  data: ChangeServiceStatusData
) => {
  return api.post(`/orders-info/${orderId}/change-service-status`, data);
};

// 计费状态变更参数接口
export interface ChangeBillingStatusData {
  bill_status: string;
  account_seq?: string;
  reality_bill_start_date?: string;
  order_start_year?: string;
  order_remark?: string;
  finished_remark?: string;
  reality_bill_end_date?: string;
}

// 变更计费状态
export const changeBillingStatus = (
  orderId: number,
  data: ChangeBillingStatusData
) => {
  return api.post(`/orders-info/${orderId}/change-billing-status`, data);
};

// 导出客户简单列表方法供合同页面使用
export { getContractsSimpleList };
