import { ApiResponse } from "../types/api";
import { StaticDataInfo, LevelStaticDataInfo } from "../types/public";
import api from "./api";

// 获取枚举值信息（下拉框使用）
export const getStaticDataList = async (query_params?: {
  word: string;
}): Promise<ApiResponse<StaticDataInfo[]>> => {
  const response = await api.get("/static-data/simple-list", {
    params: query_params,
  });
  return response.data;
};

// 获取层级静态数据
export const getLevelStaticDataList = async (query_params?: {
  word: string;
}): Promise<ApiResponse<LevelStaticDataInfo[]>> => {
  const response = await api.get("/level-static-data/simple-list", {
    params: query_params,
  });
  return response.data;
};
