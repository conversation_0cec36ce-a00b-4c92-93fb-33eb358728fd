import axios from "axios";
import { checkAuth } from "./auth";
import { useUserStore } from "../stores/user";
import { showToast } from "./toast";
import { navigateToLogin } from "./router";

// 定义 ToastServiceMethods 接口
// interface ToastServiceMethods {
//   add: (message: any) => void;
//   removeGroup: (group: string) => void;
//   removeAllGroups: () => void;
//   remove: (message: any) => void;
// }

// let toastService: ToastServiceMethods | null = null;

// export const setToastService = (service: ToastServiceMethods) => {
//   toastService = service;
// };

const api = axios.create({
  baseURL: "/api",
  timeout: 60 * 60 * 8,
  withCredentials: true,
});

// 定时器检查用户登录状态
let authCheckInterval: ReturnType<typeof setInterval> | null = null;

const startAuthCheck = () => {
  if (authCheckInterval) return;

  authCheckInterval = setInterval(async () => {
    try {
      await checkAuth();
    } catch (error) {
      const userStore = useUserStore();
      userStore.clearUser();
      navigateToLogin();
      showToast({
        severity: "error",
        summary: "错误",
        detail: "登录已过期，请重新登录",
        life: 3000,
      });
      if (authCheckInterval) {
        clearInterval(authCheckInterval);
        authCheckInterval = null;
      }
    }
  }, 200000);
};

const stopAuthCheck = () => {
  if (authCheckInterval) {
    clearInterval(authCheckInterval);
    authCheckInterval = null;
  }
};

api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      navigateToLogin();
      showToast({
        severity: "error",
        summary: "错误",
        detail: "登录已过期，请重新登录",
        life: 3000,
      });
    }
    return Promise.reject(error);
  }
);

export default api;
export { startAuthCheck, stopAuthCheck };
