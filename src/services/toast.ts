import { ToastServiceMethods } from "primevue/toastservice";

let toastService: ToastServiceMethods | null = null;

export const setToastService = (service: ToastServiceMethods) => {
  toastService = service;
};

export const showToast = ({
  severity,
  summary,
  detail,
  life = 3000,
}: {
  severity: string;
  summary: string;
  detail: string;
  life?: number;
}) => {
  toastService?.add({
    severity,
    summary,
    detail,
    life,
  });
};