<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useToast } from "primevue/usetoast";
import {
  getFeePackages,
  createFeePackage,
  updateFeePackage,
  getFeeInstances,
  createFeeInstance,
  updateFeeInstance,
  getFeeTemplatesSimpleList,
  getFeeInstanceLevels,
  createFeeInstanceLevel,
  updateFeeInstanceLevel,
  type FeePackageParams,
  type FeePackageFormData,
  type FeeInstanceParams,
} from "../../services/feePackage";
import type {
  FeePackageItem,
  FeeInstanceItem,
  FeeTemplateSimpleItem,
  FeeInstanceFormData,
  FeeInstanceLevelItem,
  FeeInstanceLevelFormData,
} from "../../types/feePackage";
import { isLevelFeeMap, isLevelFeeSeverityMap } from "../../types/feePackage";
import { formatDateTime } from "../../utils/common";
import { getStaticDataList } from "../../services/public";

const feePackages = ref<FeePackageItem[]>([]);
const selectedFeePackage = ref<FeePackageItem | null>(null);
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();

// 费用实例相关状态
const feeInstances = ref<FeeInstanceItem[]>([]);
const feeInstanceLoading = ref(false);
const feeInstanceTotalRecords = ref(0);
const feeTemplates = ref<FeeTemplateSimpleItem[]>([]);
const expandedRows = ref<Record<number, boolean>>({});

// 费用实例等级相关状态
const feeInstanceLevels = ref<Record<number, FeeInstanceLevelItem[]>>({});
const feeInstanceLevelLoading = ref<Record<number, boolean>>({});

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选参数
const filterColumns = [
  { label: "--", value: "--" },
  { label: "套餐名称", value: "package_name" },
];
const selectedFilterColumn = ref("--");
const filterValue = ref("");

// 费用实例分页和筛选参数
const feeInstanceLazyParams = ref({
  page: 1,
  pageSize: 20,
});

const feeInstanceFilterColumns = [
  { label: "--", value: "--" },
  { label: "费用", value: "fee" },
  { label: "费用模板名称", value: "fee_template_name" },
  { label: "费用单位", value: "fee_unit" },
];
const selectedFeeInstanceFilterColumn = ref("--");
const feeInstanceFilterValue = ref("");

// 表单相关
const feePackageDrawerVisible = ref(false);
const feePackageForm = ref<FeePackageFormData>({
  package_name: "",
  remark: "",
});
const editingFeePackage = ref<FeePackageItem | null>(null);
const fieldErrors = ref<Record<string, string>>({});

// 费用实例表单相关
const feeInstanceDrawerVisible = ref(false);
const feeInstanceForm = ref<FeeInstanceFormData>({
  fee_template_id: 0,
  fee: 0,
  include_amount: 0,
  over_fee: 0,
  fee_unit: "",
  speed: "",
  priority: 1,
  discount: 0,
  min_fee: 0,
  max_fee: 0,
  remark: "",
});
const editingFeeInstance = ref<FeeInstanceItem | null>(null);
const feeInstanceFieldErrors = ref<Record<string, string>>({});

// 计算属性：判断当前选择的费用模板是否为阶梯费用
const selectedFeeTemplateIsLevel = computed(() => {
  const selectedTemplate = feeTemplates.value.find(
    (template) => template.id === feeInstanceForm.value.fee_template_id
  );
  return selectedTemplate?.is_level_fee === 1;
});

// 费用实例等级表单相关
const feeInstanceLevelDrawerVisible = ref(false);
const feeInstanceLevelForm = ref<FeeInstanceLevelFormData>({
  fee: 0,
  fee_unit: "",
  month_min: 0,
  month_max: 0,
  level: 1,
  remark: "",
});
const editingFeeInstanceLevel = ref<FeeInstanceLevelItem | null>(null);
const currentFeeInstanceId = ref<number | null>(null);
const feeInstanceLevelFieldErrors = ref<Record<string, string>>({});

// 业务产品类型选项
const feeUnitOptions = ref<{ label: string; value: string }[]>([]);
// 加载业务产品类型选项
const loadfeeUnitOptions = async () => {
  try {
    const response = await getStaticDataList({ word: "fee_unit" });
    feeUnitOptions.value = response.data.map((item) => ({
      label: item.data_value,
      value: item.data_value,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载业务产品类型选项失败",
      life: 3000,
    });
  }
};

// 加载费用套餐列表数据
const loadFeePackages = async () => {
  try {
    loading.value = true;
    const params: FeePackageParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (selectedFilterColumn.value !== "--" && filterValue.value) {
      params.filter = filterValue.value;
      params.filterColumn = selectedFilterColumn.value;
    }

    const response = await getFeePackages(params);
    if (response.code === 200) {
      feePackages.value = response.data.records;
      totalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "加载费用套餐列表失败",
        life: 4000,
      });
    }
  } catch (error) {
    console.error("Failed to load fee packages:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载费用套餐列表失败",
      life: 4000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadFeePackages();
};

// 处理搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadFeePackages();
};

// 重置搜索
const resetSearch = () => {
  selectedFilterColumn.value = "--";
  filterValue.value = "";
  lazyParams.value.page = 1;
  loadFeePackages();
};

// 选择费用套餐
const selectFeePackage = (feePackage: FeePackageItem) => {
  selectedFeePackage.value = feePackage;
  // 重置费用实例分页参数并加载费用实例
  feeInstanceLazyParams.value.page = 1;
  // 清理费用实例等级相关状态
  expandedRows.value = {};
  feeInstanceLevels.value = {};
  feeInstanceLevelLoading.value = {};
  loadFeeInstances(feePackage.id);
};

// 加载费用实例列表数据
const loadFeeInstances = async (packageId: number) => {
  try {
    feeInstanceLoading.value = true;
    const params: FeeInstanceParams = {
      page: feeInstanceLazyParams.value.page,
      pageSize: feeInstanceLazyParams.value.pageSize,
    };

    if (
      selectedFeeInstanceFilterColumn.value !== "--" &&
      feeInstanceFilterValue.value
    ) {
      params.filter = feeInstanceFilterValue.value;
      params.filterColumn = selectedFeeInstanceFilterColumn.value;
    }

    const response = await getFeeInstances(packageId, params);
    if (response.code === 200) {
      feeInstances.value = response.data.records;
      feeInstanceTotalRecords.value = response.data.page.total;
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "加载费用实例列表失败",
        life: 4000,
      });
    }
  } catch (error) {
    console.error("Failed to load fee instances:", error);
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载费用实例列表失败",
      life: 4000,
    });
  } finally {
    feeInstanceLoading.value = false;
  }
};

// 处理费用实例分页事件
const onFeeInstancePage = (event: { page: number; rows: number }) => {
  feeInstanceLazyParams.value.page = event.page + 1;
  feeInstanceLazyParams.value.pageSize = event.rows;
  if (selectedFeePackage.value) {
    loadFeeInstances(selectedFeePackage.value.id);
  }
};

// 处理费用实例搜索
const handleFeeInstanceSearch = () => {
  feeInstanceLazyParams.value.page = 1;
  if (selectedFeePackage.value) {
    loadFeeInstances(selectedFeePackage.value.id);
  }
};

// 重置费用实例搜索
const resetFeeInstanceSearch = () => {
  selectedFeeInstanceFilterColumn.value = "--";
  feeInstanceFilterValue.value = "";
  feeInstanceLazyParams.value.page = 1;
  if (selectedFeePackage.value) {
    loadFeeInstances(selectedFeePackage.value.id);
  }
};

// 处理行展开事件
const onRowExpand = async (event: any) => {
  const feeInstance = event.data as FeeInstanceItem;
  await loadFeeInstanceLevels(feeInstance.id);
};

// 切换行展开状态
const toggleRowExpansion = async (rowData: FeeInstanceItem) => {
  const isExpanded = expandedRows.value[rowData.id];
  if (isExpanded) {
    // 收起行
    delete expandedRows.value[rowData.id];
  } else {
    // 展开行
    expandedRows.value[rowData.id] = true;
    await loadFeeInstanceLevels(rowData.id);
  }
};

// 检查行是否已展开
const isRowExpanded = (rowId: number) => {
  return !!expandedRows.value[rowId];
};

// 加载费用实例等级列表
const loadFeeInstanceLevels = async (instanceId: number) => {
  try {
    feeInstanceLevelLoading.value[instanceId] = true;
    const response = await getFeeInstanceLevels(instanceId);
    feeInstanceLevels.value[instanceId] = response.data;
    console.log(feeInstanceLevels.value[instanceId]);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载费用实例等级失败",
      life: 4000,
    });
  } finally {
    feeInstanceLevelLoading.value[instanceId] = false;
  }
};

// 打开新建费用套餐对话框
const openNewFeePackage = () => {
  editingFeePackage.value = null;
  feePackageForm.value = {
    package_name: "",
    remark: "",
  };
  fieldErrors.value = {};
  feePackageDrawerVisible.value = true;
};

// 打开编辑费用套餐对话框
const openEditFeePackage = (feePackage: FeePackageItem) => {
  editingFeePackage.value = feePackage;
  feePackageForm.value = {
    package_name: feePackage.package_name,
    remark: feePackage.remark || "",
  };
  fieldErrors.value = {};
  feePackageDrawerVisible.value = true;
};

// 保存费用套餐
const saveFeePackage = async () => {
  try {
    fieldErrors.value = {};
    let response;

    if (editingFeePackage.value) {
      // 编辑模式
      response = await updateFeePackage(
        editingFeePackage.value.id,
        feePackageForm.value
      );
    } else {
      // 新建模式
      response = await createFeePackage(feePackageForm.value);
    }

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: editingFeePackage.value
          ? "费用套餐更新成功"
          : "费用套餐创建成功",
        life: 3000,
      });
      feePackageDrawerVisible.value = false;
      loadFeePackages();
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "操作失败",
        life: 4000,
      });
    }
  } catch (error: any) {
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: editingFeePackage.value
          ? "费用套餐更新失败"
          : "费用套餐创建失败",
        life: 3000,
      });
    }
  }
};

// 打开新建费用实例对话框
const openNewFeeInstance = () => {
  if (!selectedFeePackage.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择费用套餐",
      life: 3000,
    });
    return;
  }

  editingFeeInstance.value = null;
  feeInstanceForm.value = {
    fee_template_id: 0,
    fee: 0,
    include_amount: 0,
    over_fee: 0,
    fee_unit: "",
    speed: "",
    priority: 1,
    discount: 0,
    min_fee: 0,
    max_fee: 0,
    remark: "",
  };
  feeInstanceFieldErrors.value = {};
  feeInstanceDrawerVisible.value = true;
};

// 打开编辑费用实例对话框
const openEditFeeInstance = (feeInstance: FeeInstanceItem) => {
  editingFeeInstance.value = feeInstance;
  // 根据feeInstance.fee_template_code查询fee_template_id
  const feeTemplate = feeTemplates.value.find(
    (item) => item.fee_template_name === feeInstance.fee_template_name
  );
  const fee_template_id = feeTemplate ? feeTemplate.id : 0;

  feeInstanceForm.value = {
    fee_template_id: fee_template_id,
    fee: feeInstance.fee,
    include_amount: feeInstance.include_amount,
    over_fee: feeInstance.over_fee,
    fee_unit: feeInstance.fee_unit,
    speed: feeInstance.speed,
    priority: feeInstance.priority,
    discount: feeInstance.discount,
    min_fee: feeInstance.min_fee,
    max_fee: feeInstance.max_fee,
    remark: feeInstance.remark || "",
  };
  feeInstanceFieldErrors.value = {};
  feeInstanceDrawerVisible.value = true;
};

// 保存费用实例
const saveFeeInstance = async () => {
  if (!selectedFeePackage.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择费用套餐",
      life: 3000,
    });
    return;
  }

  try {
    feeInstanceFieldErrors.value = {};
    let response;

    if (editingFeeInstance.value) {
      // 编辑模式
      response = await updateFeeInstance(
        selectedFeePackage.value.id,
        editingFeeInstance.value.id,
        feeInstanceForm.value
      );
    } else {
      // 新建模式
      response = await createFeeInstance(
        selectedFeePackage.value.id,
        feeInstanceForm.value
      );
    }

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: editingFeeInstance.value
          ? "费用实例更新成功"
          : "费用实例创建成功",
        life: 3000,
      });
      feeInstanceDrawerVisible.value = false;
      loadFeeInstances(selectedFeePackage.value.id);
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "操作失败",
        life: 4000,
      });
    }
  } catch (error: any) {
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      feeInstanceFieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        feeInstanceFieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(feeInstanceFieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: editingFeeInstance.value
          ? "费用实例更新失败"
          : "费用实例创建失败",
        life: 3000,
      });
    }
  }
};

// 加载费用模板简单列表
const loadFeeTemplates = async () => {
  try {
    const response = await getFeeTemplatesSimpleList();
    if (response.code === 200) {
      feeTemplates.value = response.data;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载费用模板列表失败",
      life: 3000,
    });
  }
};

// 打开新建费用实例等级对话框
const openNewFeeInstanceLevel = (feeInstance: FeeInstanceItem) => {
  currentFeeInstanceId.value = feeInstance.id;
  editingFeeInstanceLevel.value = null;
  feeInstanceLevelForm.value = {
    fee: 0,
    fee_unit: "",
    month_min: 0,
    month_max: 0,
    level: 1,
    remark: "",
  };
  feeInstanceLevelFieldErrors.value = {};
  feeInstanceLevelDrawerVisible.value = true;
};

// 打开编辑费用实例等级对话框
const openEditFeeInstanceLevel = (
  feeInstanceLevel: FeeInstanceLevelItem,
  instanceId: number
) => {
  currentFeeInstanceId.value = instanceId;
  editingFeeInstanceLevel.value = feeInstanceLevel;
  feeInstanceLevelForm.value = {
    fee: feeInstanceLevel.fee,
    fee_unit: feeInstanceLevel.fee_unit,
    month_min: feeInstanceLevel.month_min,
    month_max: feeInstanceLevel.month_max,
    level: feeInstanceLevel.level,
    remark: feeInstanceLevel.remark || "",
  };
  feeInstanceLevelFieldErrors.value = {};
  feeInstanceLevelDrawerVisible.value = true;
};

// 保存费用实例等级
const saveFeeInstanceLevel = async () => {
  if (!currentFeeInstanceId.value) {
    toast.add({
      severity: "warn",
      summary: "提示",
      detail: "请先选择费用实例",
      life: 3000,
    });
    return;
  }

  try {
    feeInstanceLevelFieldErrors.value = {};
    let response;

    if (editingFeeInstanceLevel.value) {
      // 编辑模式
      response = await updateFeeInstanceLevel(
        currentFeeInstanceId.value,
        editingFeeInstanceLevel.value.id,
        feeInstanceLevelForm.value
      );
    } else {
      // 新建模式
      response = await createFeeInstanceLevel(
        currentFeeInstanceId.value,
        feeInstanceLevelForm.value
      );
    }

    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: editingFeeInstanceLevel.value
          ? "费用实例等级更新成功"
          : "费用实例等级创建成功",
        life: 3000,
      });
      feeInstanceLevelDrawerVisible.value = false;
      loadFeeInstanceLevels(currentFeeInstanceId.value);
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: response.message || "操作失败",
        life: 4000,
      });
    }
  } catch (error: any) {
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      feeInstanceLevelFieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        feeInstanceLevelFieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(feeInstanceLevelFieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: editingFeeInstanceLevel.value
          ? "费用实例等级更新失败"
          : "费用实例等级创建失败",
        life: 3000,
      });
    }
  }
};

// 监听费用模板选择变化
watch(
  () => feeInstanceForm.value.fee_template_id,
  (newTemplateId) => {
    if (newTemplateId) {
      const selectedTemplate = feeTemplates.value.find(
        (template) => template.id === newTemplateId
      );

      // 如果选择的是阶梯费用模板，清空其他字段
      if (selectedTemplate?.is_level_fee === 1) {
        feeInstanceForm.value.fee = 0;
        feeInstanceForm.value.include_amount = 0;
        feeInstanceForm.value.over_fee = 0;
        feeInstanceForm.value.fee_unit = "";
        feeInstanceForm.value.speed = "";
        feeInstanceForm.value.priority = 1;
        feeInstanceForm.value.discount = 0;
        feeInstanceForm.value.min_fee = 0;
        feeInstanceForm.value.max_fee = 0;
      }
    }
  }
);

onMounted(() => {
  loadFeePackages();
  loadFeeTemplates();
  loadfeeUnitOptions();
});
</script>

<template>
  <div class="fee-package-container">
    <Toast />
    <div class="card">
      <div class="card-header">
        <Message variant="simple" size="large" class="mb-2"
          >费用套餐管理</Message
        >
      </div>
      <div class="fee-package-content">
        <!-- 左侧费用套餐列表 -->
        <div class="fee-package-list">
          <div class="list-header">
            <Message variant="simple">费用套餐</Message>
            <Button
              label="新增套餐"
              icon="pi pi-plus"
              class="p-button-success"
              @click="openNewFeePackage"
              data-testid="new-package-btn"
            />
          </div>

          <Toolbar class="mb-2">
            <template #start>
              <div class="flex flex-nowrap align-items-center gap-2 w-full">
                <Select
                  v-model="selectedFilterColumn"
                  :options="filterColumns"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="请选择筛选字段"
                  class="flex-shrink-0"
                  style="min-width: 150px; max-width: 200px"
                />
                <InputText
                  v-model="filterValue"
                  placeholder="请输入筛选值"
                  class="flex-grow-1"
                  style="min-width: 120px"
                  @keyup.enter="handleSearch"
                />
              </div>
            </template>
            <template #end>
              <div class="flex flex-nowrap align-items-center gap-2">
                <Button
                  icon="pi pi-search"
                  @click="handleSearch"
                  outlined
                  rounded
                  class="flex-shrink-0"
                  style="white-space: nowrap"
                />
                <Button
                  icon="pi pi-refresh"
                  outlined
                  @click="resetSearch"
                  rounded
                  class="flex-shrink-0"
                  style="white-space: nowrap"
                />
              </div>
            </template>
          </Toolbar>

          <!-- 费用套餐数据表格 -->
          <DataTable
            :value="feePackages"
            :paginator="true"
            :lazy="true"
            :loading="loading"
            :rows="20"
            :rowsPerPageOptions="[10, 20, 50]"
            :totalRecords="totalRecords"
            @page="onPage($event)"
            showGridlines
            stripedRows
            scrollable
            scrollHeight="calc(100vh - 32rem)"
            class="apple-datatable"
            selectionMode="single"
            v-model:selection="selectedFeePackage"
            @rowSelect="selectFeePackage($event.data)"
            dataKey="id"
          >
            <template #empty>
              <div class="empty-message">
                <i
                  class="pi pi-inbox"
                  style="
                    font-size: 2rem;
                    color: var(--p-text-color-secondary);
                    margin-bottom: 1rem;
                  "
                ></i>
                <p>暂无费用套餐数据</p>
              </div>
            </template>

            <Column
              field="package_name"
              header="套餐名称"
              style="min-width: 15rem"
            >
              <template #body="slotProps">
                <div
                  class="package-name"
                  v-tooltip="slotProps.data.remark || '暂无备注'"
                >
                  <i class="pi pi-tag package-icon"></i>
                  {{ slotProps.data.package_name }}
                </div>
              </template>
            </Column>

            <Column
              field="create_user"
              header="创建用户"
              style="min-width: 8rem"
            />

            <Column
              field="created_at"
              header="创建时间"
              style="min-width: 12rem"
            >
              <template #body="slotProps">
                {{ formatDateTime(slotProps.data.created_at) }}
              </template>
            </Column>

            <Column header="操作" :exportable="false" style="min-width: 8rem">
              <template #body="slotProps">
                <Button
                  icon="pi pi-pencil"
                  outlined
                  rounded
                  class="mr-2"
                  @click="openEditFeePackage(slotProps.data)"
                  v-tooltip="'编辑套餐'"
                />
              </template>
            </Column>
          </DataTable>
        </div>

        <!-- 右侧费用实例列表 -->
        <div class="fee-instance-list">
          <div class="list-header">
            <Message variant="simple"
              >费用实例{{
                selectedFeePackage ? `(${selectedFeePackage.package_name})` : ""
              }}</Message
            >
            <Button
              label="新增实例"
              icon="pi pi-plus"
              class="p-button-success"
              @click="openNewFeeInstance"
              :disabled="!selectedFeePackage"
            />
          </div>

          <div v-if="selectedFeePackage">
            <!-- 搜索筛选区域 -->
            <Toolbar class="mb-2">
              <template #start>
                <div class="flex flex-nowrap align-items-center gap-2 w-full">
                  <Select
                    v-model="selectedFeeInstanceFilterColumn"
                    :options="feeInstanceFilterColumns"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="请选择筛选字段"
                    class="flex-shrink-0"
                    style="min-width: 150px; max-width: 200px"
                  />
                  <InputText
                    v-model="feeInstanceFilterValue"
                    placeholder="请输入筛选值"
                    class="flex-grow-1"
                    style="min-width: 120px"
                    @keyup.enter="handleFeeInstanceSearch"
                  />
                </div>
              </template>
              <template #end>
                <div class="flex flex-nowrap align-items-center gap-2">
                  <Button
                    icon="pi pi-search"
                    outlined
                    rounded
                    @click="handleFeeInstanceSearch"
                    class="flex-shrink-0"
                    style="white-space: nowrap"
                  />
                  <Button
                    icon="pi pi-refresh"
                    outlined
                    rounded
                    @click="resetFeeInstanceSearch"
                    class="flex-shrink-0"
                    style="white-space: nowrap"
                  />
                </div>
              </template>
            </Toolbar>

            <!-- 费用实例数据表格 -->
            <DataTable
              :value="feeInstances"
              :paginator="true"
              :lazy="true"
              :loading="feeInstanceLoading"
              :rows="20"
              :rowsPerPageOptions="[10, 20, 50]"
              :totalRecords="feeInstanceTotalRecords"
              @page="onFeeInstancePage($event)"
              showGridlines
              stripedRows
              scrollable
              scrollHeight="calc(100vh - 32rem)"
              class="apple-datatable"
              v-model:expandedRows="expandedRows"
              @row-expand="onRowExpand"
              rowGroupMode="subheader"
              dataKey="id"
              style="overflow-x: auto"
            >
              <template #empty>
                <div class="empty-message">
                  <i
                    class="pi pi-inbox"
                    style="
                      font-size: 2rem;
                      color: var(--p-text-color-secondary);
                      margin-bottom: 1rem;
                    "
                  ></i>
                  <p>暂无费用实例数据</p>
                </div>
              </template>
              <Column style="width: 3rem; flex-shrink: 0">
                <template #body="slotProps">
                  <Button
                    v-if="slotProps.data.is_level_fee === 1"
                    :icon="
                      isRowExpanded(slotProps.data.id)
                        ? 'pi pi-chevron-down'
                        : 'pi pi-chevron-right'
                    "
                    text
                    rounded
                    size="small"
                    @click="toggleRowExpansion(slotProps.data)"
                  />
                </template>
              </Column>
              <Column
                field="fee_template_name"
                header="费用模板"
                style="min-width: 8rem; max-width: 12rem"
              >
                <template #body="slotProps">
                  <div
                    style="
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    :title="slotProps.data.fee_template_name"
                  >
                    {{ slotProps.data.fee_template_name }}
                  </div>
                </template>
              </Column>
              <Column
                field="fee"
                header="费用(元)"
                style="min-width: 6rem; max-width: 8rem"
              />
              <Column
                field="over_fee"
                header="超出费用"
                style="min-width: 6rem; max-width: 8rem"
              />
              <Column
                field="fee_unit"
                header="费用单位"
                style="min-width: 5rem; max-width: 7rem"
              />
              <Column
                field="include_amount"
                header="包含量"
                style="min-width: 5rem; max-width: 7rem"
              />
              <Column
                field="discount"
                header="折扣"
                style="min-width: 4rem; max-width: 6rem"
              >
                <template #body="slotProps">
                  {{ (slotProps.data.discount * 100).toFixed(0) }}%
                </template>
              </Column>

              <Column
                field="is_level_fee"
                header="阶梯费用"
                style="min-width: 6rem; max-width: 8rem"
              >
                <template #body="slotProps">
                  <Tag
                    :severity="
                      isLevelFeeSeverityMap[slotProps.data.is_level_fee] ||
                      'info'
                    "
                    :value="isLevelFeeMap[slotProps.data.is_level_fee]"
                  />
                </template>
              </Column>
              <Column header="备注" , style="min-width: 5rem; max-width: 5rem">
                <template #body="slotProps">
                  <div
                    style="
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    v-tooltip="slotProps.data.remark || '暂无备注'"
                  >
                    {{ slotProps.data.remark }}
                  </div>
                </template>
              </Column>
              <Column
                header="操作"
                :exportable="false"
                style="width: 8rem; flex-shrink: 0"
                alignFrozen="right"
                frozen
              >
                <template #body="slotProps">
                  <div class="flex gap-1">
                    <Button
                      icon="pi pi-pencil"
                      outlined
                      rounded
                      size="small"
                      @click="openEditFeeInstance(slotProps.data)"
                      v-tooltip="'编辑实例'"
                    />
                    <Button
                      v-if="slotProps.data.is_level_fee === 1"
                      icon="pi pi-sort-numeric-up-alt"
                      outlined
                      rounded
                      size="small"
                      severity="help"
                      @click="openNewFeeInstanceLevel(slotProps.data)"
                      v-tooltip="'新建等级'"
                    />
                  </div>
                </template>
              </Column>

              <!-- 展开行模板 -->
              <template #expansion="slotProps">
                <div class="p-3" v-if="slotProps.data.is_level_fee === 1">
                  <div class="flex justify-between items-center mb-3">
                    <Tag value="费用实例等级列表" />
                  </div>
                  <DataTable
                    :value="feeInstanceLevels[slotProps.data.id] || []"
                    :loading="
                      feeInstanceLevelLoading[slotProps.data.id] || false
                    "
                    stripedRows
                    showGridlines
                    class="nested-datatable"
                  >
                    <template #empty>
                      <div class="empty-message">
                        <i
                          class="pi pi-inbox"
                          style="
                            font-size: 1.5rem;
                            color: var(--p-text-color-secondary);
                            margin-bottom: 0.5rem;
                          "
                        ></i>
                        <p>暂无费用实例等级数据</p>
                      </div>
                    </template>

                    <Column
                      field="level"
                      header="等级"
                      style="min-width: 6rem"
                    />

                    <Column field="fee" header="费用" style="min-width: 8rem" />
                    <Column
                      field="fee_unit"
                      header="费用单位"
                      style="min-width: 8rem"
                    />
                    <Column
                      field="month_min"
                      header="月最小值"
                      style="min-width: 8rem"
                    />
                    <Column
                      field="month_max"
                      header="月最大值"
                      style="min-width: 8rem"
                    />  
                    <Column
                      field="created_at"
                      header="创建时间"
                      style="min-width: 12rem"
                    >
                      <template #body="levelSlotProps">
                        {{ formatDateTime(levelSlotProps.data.created_at) }}
                      </template>
                    </Column>

                    <Column
                      header="操作"
                      :exportable="false"
                      style="min-width: 8rem"
                    >
                      <template #body="levelSlotProps">
                        <Button
                          icon="pi pi-pencil"
                          outlined
                          rounded
                          size="small"
                          @click="
                            openEditFeeInstanceLevel(
                              levelSlotProps.data,
                              slotProps.data.id
                            )
                          "
                          v-tooltip="'编辑等级'"
                        />
                      </template>
                    </Column>
                  </DataTable>
                </div>
              </template>
            </DataTable>
          </div>

          <div class="no-selection" v-else>
            <i
              class="pi pi-info-circle"
              style="
                font-size: 2rem;
                color: var(--text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>请选择左侧费用套餐查看费用实例</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建/编辑费用套餐 Drawer -->
    <Drawer
      v-model:visible="feePackageDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="true"
      :showCloseIcon="true"
      :header="editingFeePackage ? '编辑费用套餐' : '新增费用套餐'"
      class="p-fluid fee-package-drawer"
    >
      <Fluid>
        <div class="grid grid-cols-1 gap-4">
          <div class="field">
            <label for="package_name" class="required">
              套餐名称
              <span v-if="fieldErrors.package_name" class="p-error ml-2">{{
                fieldErrors.package_name
              }}</span>
            </label>
            <InputText
              id="package_name"
              v-model="feePackageForm.package_name"
              required
              :class="{ 'p-invalid': fieldErrors.package_name }"
              placeholder="请输入套餐名称"
            />
          </div>
          <div class="field">
            <label for="remark">备注</label>
            <Textarea
              id="remark"
              v-model="feePackageForm.remark"
              rows="4"
              placeholder="请输入备注信息（可选）"
            />
          </div>
        </div>
      </Fluid>
      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          text
          @click="feePackageDrawerVisible = false"
          class="mr-2"
        />
        <Button
          label="保存"
          icon="pi pi-check"
          @click="saveFeePackage"
          class="p-button-success"
        />
      </template>
    </Drawer>

    <!-- 新建/编辑费用实例 Drawer -->
    <Drawer
      v-model:visible="feeInstanceDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="true"
      :showCloseIcon="true"
      :header="editingFeeInstance ? '编辑费用实例' : '新增费用实例'"
      class="p-fluid fee-instance-drawer"
    >
      <!-- 阶梯费用提示 -->
      <Message v-if="selectedFeeTemplateIsLevel" severity="warn" class="mb-4">
        <i class="pi pi-info-circle mr-2"></i>
        当前选择的费用模板为阶梯费用，除备注外的其他字段将被禁用。请通过费用实例等级来配置具体的费用信息。
      </Message>
      <Fluid>
        <div class="grid grid-cols-2 gap-4">
          <div class="field">
            <label for="fee_template_id" class="required">
              费用模板
              <span
                v-if="feeInstanceFieldErrors.fee_template_id"
                class="p-error ml-2"
                >{{ feeInstanceFieldErrors.fee_template_id }}</span
              >
            </label>
            <Select
              id="fee_template_id"
              v-model="feeInstanceForm.fee_template_id"
              :options="feeTemplates"
              optionLabel="fee_template_name"
              optionValue="id"
              placeholder="请选择费用模板"
              :class="{ 'p-invalid': feeInstanceFieldErrors.fee_template_id }"
            />
          </div>
          <div class="field">
            <label for="fee" class="required">
              费用
              <span v-if="feeInstanceFieldErrors.fee" class="p-error ml-2">{{
                feeInstanceFieldErrors.fee
              }}</span>
            </label>
            <InputNumber
              id="fee"
              v-model="feeInstanceForm.fee"
              :min="0"
              :step="0.0001"
              :minFractionDigits="4"
              showButtons
              :class="{ 'p-invalid': feeInstanceFieldErrors.fee }"
              placeholder="请输入费用"
              :disabled="selectedFeeTemplateIsLevel"
            />
          </div>
          <div class="field">
            <label for="include_amount">包含量</label>
            <InputNumber
              id="include_amount"
              v-model="feeInstanceForm.include_amount"
              :min="0"
              showButtons
              placeholder="请输入包含量"
              :disabled="selectedFeeTemplateIsLevel"
            />
          </div>
          <div class="field">
            <label for="over_fee">超出费用</label>
            <InputNumber
              id="over_fee"
              v-model="feeInstanceForm.over_fee"
              :min="0"
              :step="0.0001"
              :minFractionDigits="4"
              placeholder="请输入超出费用"
              :disabled="selectedFeeTemplateIsLevel"
            />
          </div>
          <div class="field">
            <label for="fee_unit">费用单位</label>
            <Select
              v-model="feeInstanceForm.fee_unit"
              :options="feeUnitOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="请选择费用单位"
              :disabled="selectedFeeTemplateIsLevel"
            />
          </div>
          <div class="field">
            <label for="discount">折扣</label>
            <InputNumber
              id="discount"
              v-model="feeInstanceForm.discount"
              :min="0"
              :max="1"
              :step="0.01"
              showButtons
              placeholder="请输入折扣(0-1)"
              :disabled="selectedFeeTemplateIsLevel"
            />
          </div>
          <div class="field">
            <label for="min_fee">最低费用</label>
            <InputNumber
              id="min_fee"
              v-model="feeInstanceForm.min_fee"
              :min="0"
              placeholder="请输入最低费用"
              :disabled="selectedFeeTemplateIsLevel"
            />
          </div>
          <div class="field">
            <label for="max_fee">封顶费用</label>
            <InputNumber
              id="max_fee"
              v-model="feeInstanceForm.max_fee"
              :min="0"
              placeholder="请输入封顶费用"
              :disabled="selectedFeeTemplateIsLevel"
            />
          </div>
          <div class="field col-span-2">
            <label for="remark">备注</label>
            <Textarea
              id="remark"
              v-model="feeInstanceForm.remark"
              rows="3"
              placeholder="请输入备注信息（可选）"
            />
          </div>
        </div>
      </Fluid>
      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          text
          @click="feeInstanceDrawerVisible = false"
          class="mr-2"
        />
        <Button
          label="保存"
          icon="pi pi-check"
          @click="saveFeeInstance"
          class="p-button-success"
        />
      </template>
    </Drawer>

    <!-- 新建/编辑费用实例等级 Drawer -->
    <Drawer
      v-model:visible="feeInstanceLevelDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="true"
      :showCloseIcon="true"
      :header="
        editingFeeInstanceLevel ? '编辑费用实例等级' : '新增费用实例等级'
      "
      class="p-fluid fee-instance-level-drawer"
    >
      <Fluid>
        <div class="grid grid-cols-2 gap-4">
          <div class="field">
            <label for="level" class="required">
              等级
              <span
                v-if="feeInstanceLevelFieldErrors.level"
                class="p-error ml-2"
                >{{ feeInstanceLevelFieldErrors.level }}</span
              >
            </label>
            <InputNumber
              id="level"
              v-model="feeInstanceLevelForm.level"
              :min="1"
              :max="10"
              showButtons
              :class="{ 'p-invalid': feeInstanceLevelFieldErrors.level }"
              placeholder="请输入等级"
            />
          </div>
          <div class="field">
            <label for="fee" class="required">
              费用
              <span
                v-if="feeInstanceLevelFieldErrors.fee"
                class="p-error ml-2"
                >{{ feeInstanceLevelFieldErrors.fee }}</span
              >
            </label>
            <InputNumber
              id="fee"
              v-model="feeInstanceLevelForm.fee"
              :min="0"
              :step="0.0001"
              :minFractionDigits="4"
              showButtons
              :class="{ 'p-invalid': feeInstanceLevelFieldErrors.fee }"
              placeholder="请输入费用"
            />
          </div>
          <div class="field">
            <label for="fee_unit">费用单位</label>
            <Select
              v-model="feeInstanceLevelForm.fee_unit"
              :options="feeUnitOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="请选择费用单位"
            />
          </div>
          <div class="field">
            <label for="month_min">月最小值</label>
            <InputNumber
              id="month_min"
              v-model="feeInstanceLevelForm.month_min"
              :min="0"
              placeholder="请输入月最小值"
            />
          </div>
          <div class="field">
            <label for="month_max">月最大值</label>
            <InputNumber
              id="month_max"
              v-model="feeInstanceLevelForm.month_max"
              :min="0"
              placeholder="请输入月最大值"
            />
          </div>
          <div class="field col-span-2">
            <label for="remark">备注</label>
            <Textarea
              id="remark"
              v-model="feeInstanceLevelForm.remark"
              rows="3"
              placeholder="请输入备注信息（可选）"
            />
          </div>
        </div>
      </Fluid>
      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          text
          @click="feeInstanceLevelDrawerVisible = false"
          class="mr-2"
        />
        <Button
          label="保存"
          icon="pi pi-check"
          @click="saveFeeInstanceLevel"
          class="p-button-success"
        />
      </template>
    </Drawer>
  </div>
</template>

<style scoped>
.fee-package-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.fee-package-content {
  display: flex;
  gap: 1rem;
  flex: 1;
  min-height: 0;
}

.fee-package-list {
  flex: 1;
  border: 1px solid var(--surface-border);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

.fee-instance-list {
  flex: 2;
  border: 1px solid var(--surface-border);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

.list-header,
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.package-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.package-icon {
  color: var(--primary-color);
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
  height: 300px;
}

.no-selection p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.detail-content {
  height: calc(100vh - 35rem);
  overflow-y: auto;
}

.detail-info h3 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.info-grid {
  display: grid;
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-weight: 500;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
}

.info-item span {
  color: var(--text-color);
  font-size: 1rem;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--p-primary-color);
}

.field label.required::after {
  content: " *";
  color: var(--p-red-500);
}

.mr-2 {
  margin-right: 0.5rem;
}

:deep(.p-error) {
  color: var(--p-red-500);
}

:deep(.p-invalid) {
  border-color: var(--p-red-500);
}

/* Apple风格的DataTable */
.apple-datatable {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.apple-datatable .p-datatable-header) {
  background: var(--surface-ground);
  border-bottom: 1px solid var(--surface-border);
}

:deep(.apple-datatable .p-datatable-thead > tr > th) {
  background: var(--surface-ground);
  color: var(--text-color);
  font-weight: 600;
  border-bottom: 1px solid var(--surface-border);
  padding: 1rem;
}

:deep(.apple-datatable .p-datatable-tbody > tr > td) {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--surface-border);
}

:deep(.apple-datatable .p-datatable-tbody > tr:hover) {
  background: var(--surface-hover);
}

:deep(.apple-datatable .p-datatable-tbody > tr.p-highlight) {
  background: var(--primary-50);
  color: var(--primary-color);
}

/* 嵌套DataTable样式 */
.nested-datatable {
  overflow: hidden;
  margin-top: 0.5rem;
}

:deep(.nested-datatable .p-datatable-header) {
  background: var(--surface-50);
  border-bottom: 1px solid var(--surface-border);
}

:deep(.nested-datatable .p-datatable-thead > tr > th) {
  background: var(--surface-50);
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.75rem;
  border-bottom: 1px solid var(--surface-border);
}

:deep(.nested-datatable .p-datatable-tbody > tr > td) {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid var(--surface-border);
  font-size: 0.9rem;
}

:deep(.nested-datatable .p-datatable-tbody > tr:hover) {
  background: var(--surface-hover);
}

:deep(.nested-datatable .empty-message) {
  padding: 1rem;
  text-align: center;
}

:deep(.nested-datatable .empty-message p) {
  margin: 0;
  font-size: 0.9rem;
}
</style>
