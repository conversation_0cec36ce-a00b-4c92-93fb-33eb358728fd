<script setup lang="ts">
import { ref, watch } from "vue";
import type { ContractItem } from "../../types/contract";
import { contractTermMap } from "../../utils/const";

const props = defineProps<{
  visible: boolean;
  contract: ContractItem | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const dialogVisible = ref(false);

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
  }
);

// 监听内部状态变化
watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
});

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 格式化金额
const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return "--";
  return `¥${amount.toLocaleString()}`;
};

// 格式化是否字段
const formatYesNo = (value: string | null | undefined) => {
  if (value === "Y") return "是";
  if (value === "N") return "否";
  return "--";
};
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    modal
    :header="`合同详情 - ${contract?.contract_title || ''}`"
    :style="{ width: '80rem' }"
    class="apple-contract-detail-dialog"
    :closable="true"
    :dismissableMask="true"
    @hide="closeDialog"
    maximizable
  >
    <div v-if="contract" class="contract-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-info-circle"></i>
          基本信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>合同编码</label>
              <span class="detail-value">{{ contract.contract_num }}</span>
            </div>
            <div class="detail-item">
              <label>合同标题</label>
              <span class="detail-value">{{ contract.contract_title }}</span>
            </div>
            <div class="detail-item">
              <label>客户编码</label>
              <span class="detail-value">{{ contract.main_customer_num }}</span>
            </div>
            <div class="detail-item">
              <label>业务类型</label>
              <span class="detail-value">{{ contract.business || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>合同类型</label>
              <span class="detail-value">{{
                contract.contract_type || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>销售</label>
              <span class="detail-value">{{ contract.sale_name || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>状态</label>
              <span class="detail-value">{{ contract.state || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>签约主体</label>
              <span class="detail-value">{{
                contract.sign_contract_entity || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 合同条款 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-bars"></i>
          合同条款
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>合同期限</label>
              <span class="detail-value">{{
                contractTermMap[contract.contract_term] || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同开始日期</label>
              <span class="detail-value">{{
                contract.contract_start_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同结束日期</label>
              <span class="detail-value">{{
                contract.contract_end_date || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同生效月数</label>
              <span class="detail-value">{{
                contract.contract_effect_months || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同月度金额</label>
              <span class="detail-value">{{
                formatCurrency(contract.contract_month_amount)
              }}</span>
            </div>
            <div class="detail-item">
              <label>是否自动延期</label>
              <span class="detail-value">{{
                formatYesNo(contract.is_auto_delay)
              }}</span>
            </div>
            <div class="detail-item">
              <label>自动延期次数</label>
              <span class="detail-value">{{
                contract.auto_delay_num || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 编码信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-code"></i>
          编码信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>别名客户编码</label>
              <span class="detail-value">{{
                contract.alias_customer_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>对方编码</label>
              <span class="detail-value">{{
                contract.other_party_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>框架合同编码</label>
              <span class="detail-value">{{
                contract.frame_contract_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同法务编码</label>
              <span class="detail-value">{{
                contract.contract_legal_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>框架法务编码</label>
              <span class="detail-value">{{
                contract.frame_legal_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同OA编码</label>
              <span class="detail-value">{{
                contract.contract_oa_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>框架OA编码</label>
              <span class="detail-value">{{
                contract.frame_oa_num || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>上传文件</label>
              <span class="detail-value">{{
                contract.upload_file || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 补充信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-file-edit"></i>
          补充信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-1 gap-3">
            <div class="detail-item">
              <label>合同摘要</label>
              <div class="detail-text">
                {{ contract.contract_summary || "--" }}
              </div>
            </div>
            <div class="detail-item">
              <label>补充合同方式</label>
              <span class="detail-value">{{
                contract.append_contract_way || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>补充合同说明</label>
              <div class="detail-text">
                {{ contract.append_contract_explain || "--" }}
              </div>
            </div>
            <div class="detail-item">
              <label>备注</label>
              <div class="detail-text">{{ contract.remark || "--" }}</div>
            </div>
          </div>
        </Fluid>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button
          label="关闭"
          icon="pi pi-times"
          @click="closeDialog"
          class="apple-button"
          severity="secondary"
          outlined
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
/* 苹果风格对话框样式 */
:deep(.apple-contract-detail-dialog) {
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

:deep(.apple-contract-detail-dialog .p-dialog-header) {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 1.5rem 2rem !important;
}

:deep(.apple-contract-detail-dialog .p-dialog-title) {
  font-weight: 600 !important;
  font-size: 1.25rem !important;
  color: #1d1d1f !important;
}

:deep(.apple-contract-detail-dialog .p-dialog-content) {
  padding: 0 !important;
  background: #fbfbfd !important;
}

:deep(.apple-contract-detail-dialog .p-dialog-footer) {
  background: #ffffff !important;
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 0 0 12px 12px !important;
  padding: 1.5rem 2rem !important;
}

.contract-detail-content {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
  background: #fbfbfd;
}

.detail-section {
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #b2edc8 0%, #9cf5bd 100%);
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.section-title i {
  color: #ffffff;
  font-size: 1rem;
}

.detail-section .p-fluid {
  padding: 2rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #6e6e73;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.detail-text {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 3rem;
  white-space: pre-wrap;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.apple-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.apple-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 滚动条样式 */
.contract-detail-content::-webkit-scrollbar {
  width: 6px;
}

.contract-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.contract-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.contract-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
