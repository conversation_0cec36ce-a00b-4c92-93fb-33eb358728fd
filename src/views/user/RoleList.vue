<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import { getRoles, createRole, updateRole } from "../../services/role";
import { getMenus } from "../../services/menu";
import type { RoleMenu, Role } from "../../types/role";
import type { TreeNode } from "primevue/treenode";
import type { Menu } from "../../types/menu";

const roles = ref<Role[]>([]);
const loading = ref(false);
const toast = useToast();
const submitted = ref(false);
// 添加筛选相关变量
const word = ref("");

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 10,
});
const totalRecords = ref(0);

// 新增/编辑角色相关
const roleDrawerVisible = ref(false);
const roleForm = ref<{
  id?: number;
  role_name: string;
  role_code: string;
  menus: { menu_id: number; operation: boolean | null }[];
}>({
  role_name: "",
  role_code: "",
  menus: [],
});
const allMenus = ref<TreeNode[]>([]);
const selectedMenus = ref<{
  [key: string]: { checked: boolean; partialChecked: boolean };
}>({});
const fieldErrors = ref<{ [key: string]: string }>({});

// 菜单配置相关
const dialogVisible = ref(false);
const selectedRole = ref<Role | null>(null);
const roleMenus = ref<TreeNode[]>([]);
const headerValue = ref("");

// 转换菜单数据为 TreeNode 格式
const convertToTreeNodes = (data: Menu[] | RoleMenu[]): TreeNode[] => {
  return data.map((menu) => ({
    key: menu.id.toString(),
    data: {
      menu_name: menu.menu_name,
      icon: menu.icon,
      operation: "operation" in menu ? menu.operation : false,
    },
    children: menu.children ? convertToTreeNodes(menu.children) : undefined,
  }));
};

// 加载角色数据
const loadRoles = async () => {
  try {
    loading.value = true;
    const params: { page: number; pageSize: number; word?: string } = {
      ...lazyParams.value,
    };
    // 只有当word有值时才添加到params中
    if (word.value) {
      params.word = word.value;
    }
    const response = await getRoles(params);
    roles.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载角色数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 加载所有菜单
const loadAllMenus = async () => {
  try {
    const response = await getMenus();
    allMenus.value = convertToTreeNodes(response.data);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载菜单数据失败",
      life: 3000,
    });
  }
};

// 打开新增角色抽屉
const openNew = () => {
  roleForm.value = {
    role_name: "",
    role_code: "",
    menus: [],
  };
  selectedMenus.value = {};
  roleDrawerVisible.value = true;
  fieldErrors.value = {};
  loadAllMenus();
};

// 处理菜单选择
const handleMenuSelection = (node: TreeNode) => {
  const menuId = parseInt(node.key as string);
  const isSelected = selectedMenus.value[node.key as string]?.checked;
  // 处理选中逻辑
  if (isSelected) {
    // 如果是一级菜单，添加所有子菜单
    if (node.children) {
      // 添加父级菜单（如果不存在）
      if (!roleForm.value.menus.some((m) => m.menu_id === menuId)) {
        roleForm.value.menus.push({
          menu_id: menuId,
          operation: null,
        });
      }
      // 添加所有子菜单
      const addChildrenMenus = (children: TreeNode[]) => {
        children.forEach((child) => {
          const childId = parseInt(child.key as string);
          if (!roleForm.value.menus.some((m) => m.menu_id === childId)) {
            roleForm.value.menus.push({
              menu_id: childId,
              operation: false,
            });
          }
          if (child.children) {
            addChildrenMenus(child.children);
          }
        });
      };
      addChildrenMenus(node.children);
    } else {
      // 如果是子菜单，确保父级菜单也被添加
      const parentNode = findParentNode(allMenus.value, node.key as string);
      if (parentNode) {
        const parentId = parseInt(parentNode.key as string);
        // 检查父级菜单是否已存在
        if (!roleForm.value.menus.some((m) => m.menu_id === parentId)) {
          roleForm.value.menus.push({
            menu_id: parentId,
            operation: null,
          });
        }
      }
      // 添加当前子菜单（如果不存在）
      if (!roleForm.value.menus.some((m) => m.menu_id === menuId)) {
        roleForm.value.menus.push({
          menu_id: menuId,
          operation: false,
        });
      }
    }
  } else {
    // 处理取消选中逻辑
    if (node.children) {
      // 如果是一级菜单，移除所有子菜单
      const removeChildrenMenus = (children: TreeNode[]) => {
        children.forEach((child) => {
          const index = roleForm.value.menus.findIndex(
            (m) => m.menu_id === parseInt(child.key as string)
          );
          if (index !== -1) {
            roleForm.value.menus.splice(index, 1);
          }
          // 同步重置operation为false
          child.data.operation = false;
          if (child.children) {
            removeChildrenMenus(child.children);
          }
        });
      };
      removeChildrenMenus(node.children);
      // 移除父级菜单
      const index = roleForm.value.menus.findIndex((m) => m.menu_id === menuId);
      if (index !== -1) {
        roleForm.value.menus.splice(index, 1);
      }
      // 同步重置父节点operation为false
      node.data.operation = false;
    } else {
      // 如果是子菜单，移除当前菜单
      const index = roleForm.value.menus.findIndex((m) => m.menu_id === menuId);
      if (index !== -1) {
        roleForm.value.menus.splice(index, 1);
      }
      // 同步重置当前节点operation为false
      node.data.operation = false;
      // 检查是否需要移除父级菜单
      const parentNode = findParentNode(allMenus.value, node.key as string);
      if (parentNode) {
        const parentId = parseInt(parentNode.key as string);
        // 检查是否还有其他选中的子菜单
        const hasOtherSelectedChildren = roleForm.value.menus.some(
          (m) =>
            m.menu_id !== menuId &&
            m.menu_id !== parentId &&
            isChildOfNode(parentNode, m.menu_id.toString())
        );
        if (!hasOtherSelectedChildren) {
          const parentIndex = roleForm.value.menus.findIndex(
            (m) => m.menu_id === parentId
          );
          if (parentIndex !== -1) {
            roleForm.value.menus.splice(parentIndex, 1);
          }
        }
      }
    }
  }
  console.log(roleForm.value.menus);
  console.log(selectedMenus.value);
};

// 查找父节点
const findParentNode = (
  nodes: TreeNode[],
  targetKey: string
): TreeNode | null => {
  for (const node of nodes) {
    if (node.children) {
      const found = node.children.some((child) => child.key === targetKey);
      if (found) return node;
      const result = findParentNode(node.children, targetKey);
      if (result) return result;
    }
  }
  return null;
};

// 检查是否是节点的子节点
const isChildOfNode = (parentNode: TreeNode, targetKey: string): boolean => {
  if (!parentNode.children) return false;
  return parentNode.children.some((child) => {
    if (child.key === targetKey) return true;
    return isChildOfNode(child, targetKey);
  });
};

// 保存角色
const saveRole = async () => {
  if (!roleForm.value.role_name || !roleForm.value.role_code) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "请填写完整的角色信息",
      life: 3000,
    });
    return;
  }
  try {
    if (roleForm.value.id) {
      // 编辑角色时调用updateRole方法
      await updateRole(roleForm.value, roleForm.value.id);
    } else {
      // 新增角色时调用createRole方法
      await createRole(roleForm.value);
    }
    roleDrawerVisible.value = false;
    toast.add({
      severity: "success",
      summary: "成功",
      detail: roleForm.value.id ? "角色更新成功" : "角色创建成功",
      life: 3000,
    });
    loadRoles();
  } catch (error: any) {
    if (error.response.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: roleForm.value.id ? "更新角色失败" : "创建角色失败",
        life: 3000,
      });
    }
  }
};

// 打开菜单配置
const openMenuConfig = (role: Role) => {
  selectedRole.value = role;
  roleMenus.value = convertToTreeNodes(role.menus);
  headerValue.value = "菜单配置 - " + role?.role_name;
  dialogVisible.value = true;
};

// 处理操作权限变更
const handleOperationChange = (node: TreeNode, value: boolean) => {
  const menuId = parseInt(node.key as string);
  const index = roleForm.value.menus.findIndex((m) => m.menu_id === menuId);
  if (index !== -1) {
    roleForm.value.menus[index].operation = value;
  }
};

// 打开修改角色
const editRole = async (role: Role) => {
  roleForm.value = {
    id: role.id,
    role_name: role.role_name,
    role_code: role.role_code,
    menus: [],
  };
  // 打开抽屉并先加载菜单数据
  roleDrawerVisible.value = true;
  fieldErrors.value = {};
  await loadAllMenus();
  // 初始化selectedMenus
  selectedMenus.value = {};

  // 递归处理菜单及其子菜单
  const processMenus = (menus: RoleMenu[]) => {
    menus.forEach((menu) => {
      // 添加当前菜单到roleForm
      roleForm.value.menus.push({
        menu_id: menu.id,
        operation: menu.operation,
      });

      // 更新allMenus中对应节点的operation属性
      updateNodeOperation(allMenus.value, menu.id.toString(), menu.operation);

      // 递归处理子菜单
      if (menu.children && menu.children.length > 0) {
        processMenus(menu.children);

        // 从allMenus中找到当前菜单节点
        const findNodeInAllMenus = (
          nodes: TreeNode[],
          targetId: string
        ): TreeNode | null => {
          for (const node of nodes) {
            if (node.key === targetId) return node;
            if (node.children) {
              const found = findNodeInAllMenus(node.children, targetId);
              if (found) return found;
            }
          }
          return null;
        };

        const currentNode = findNodeInAllMenus(
          allMenus.value,
          menu.id.toString()
        );
        console.log(currentNode);
        if (currentNode && currentNode.children) {
          // 检查子菜单是否全部选中
          const allChildrenSelected = currentNode.children.every((child) =>
            roleForm.value.menus.some(
              (m) => m.menu_id === parseInt(child.key as string)
            )
          );

          // 设置父菜单的选中状态
          selectedMenus.value[menu.id.toString()] = {
            checked: allChildrenSelected,
            partialChecked:
              !allChildrenSelected &&
              currentNode.children.some((child) =>
                roleForm.value.menus.some(
                  (m) => m.menu_id === parseInt(child.key as string)
                )
              ),
          };
        }
      } else {
        // 叶子节点直接设置为选中
        selectedMenus.value[menu.id.toString()] = {
          checked: true,
          partialChecked: false,
        };
      }
    });
  };

  // 处理所有菜单（包括子菜单）
  processMenus(role.menus);
};

// 更新节点的operation属性
const updateNodeOperation = (
  nodes: TreeNode[],
  key: string,
  operation: boolean | null
) => {
  for (const node of nodes) {
    if (node.key === key) {
      node.data.operation = operation;
      return true;
    }
    if (node.children) {
      if (updateNodeOperation(node.children, key, operation)) {
        return true;
      }
    }
  }
  return false;
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadRoles();
};

onMounted(() => {
  loadRoles();
});
</script>

<template>
  <div class="role-list-container">
    <Toast />
    <div class="card">
      <div class="card-header">
        <Message variant="simple" size="large">角色管理</Message>
        <Button
          label="新增角色"
          icon="pi pi-plus"
          class="p-button-success"
          @click="openNew"
        />
      </div>

      <!-- 添加筛选区域 -->
      <Toolbar class="mb-2">
        <template #center>
          <FloatLabel>
            <InputText id="roleName" v-model="word" @keyup.enter="loadRoles" />
            <label for="roleName">角色名称</label>
          </FloatLabel>
        </template>
      </Toolbar>

      <DataTable
        :value="roles"
        :lazy="true"
        :paginator="true"
        :rows="10"
        :totalRecords="totalRecords"
        :loading="loading"
        :rowsPerPageOptions="[10, 20, 50]"
        @page="onPage"
        stripedRows
        showGridlines
        scrollable
        scrollHeight="calc(100vh - 25rem)"
      >
        <Column field="role_name" header="角色名称" />
        <Column field="role_code" header="角色编码" />
        <Column header="操作">
          <template #body="slotProps">
            <Button
              icon="pi pi-pencil"
              outlined
              rounded
              @click="editRole(slotProps.data)"
              v-tooltip.top="'修改角色'"
              class="mr-2"
            />
            <Button
              icon="pi pi-list"
              rounded
              outlined
              severity="help"
              @click="openMenuConfig(slotProps.data)"
              v-tooltip.top="'菜单信息'"
            />
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- 新增/编辑角色抽屉 -->
    <Drawer
      v-model:visible="roleDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="true"
      :showCloseIcon="true"
      :header="roleForm.id ? '编辑角色' : '新增角色'"
    >
      <Fluid>
        <div class="grid grid-cols-1 gap-2">
          <div class="field">
            <label for="role_name" class="required">
              角色名称
              <span v-if="fieldErrors.role_name" class="p-error ml-2">{{
                fieldErrors.role_name
              }}</span>
            </label>
            <InputText
              id="role_name"
              v-model="roleForm.role_name"
              class="w-full"
              :class="{
                'p-invalid':
                  (submitted && !roleForm.role_name) || fieldErrors.role_name,
              }"
            />
            <small class="p-error" v-if="submitted && !roleForm.role_name"
              >角色名称不能为空</small
            >
          </div>
          <div class="field">
            <label for="role_code" class="required">
              角色编码
              <span v-if="fieldErrors.role_code" class="p-error ml-2">{{
                fieldErrors.role_code
              }}</span>
            </label>
            <InputText
              id="role_code"
              v-model="roleForm.role_code"
              class="w-full"
              :class="{
                'p-invalid':
                  (submitted && !roleForm.role_code) || fieldErrors.role_code,
              }"
            />
            <small class="p-error" v-if="submitted && !roleForm.role_code"
              >角色编码不能为空</small
            >
          </div>

          <div class="field">
            <label class="required">菜单权限</label>
            <TreeTable
              :value="allMenus"
              v-model:selectionKeys="selectedMenus"
              selectionMode="checkbox"
              class="w-full"
              @nodeSelect="handleMenuSelection"
              @nodeUnselect="handleMenuSelection"
              showGridlines
            >
              <Column field="menu_name" header="菜单" expander>
                <template #body="slotProps">
                  <div>
                    <i :class="slotProps.node.data.icon" class="mr-2"></i>
                    <span>{{ slotProps.node.data.menu_name }}</span>
                  </div>
                </template>
              </Column>
              <Column field="operation" header="操作权限">
                <template #body="slotProps">
                  <ToggleSwitch
                    v-if="slotProps.node.children"
                    v-model="slotProps.node.data.operation"
                    :disabled="true"
                  />
                  <ToggleSwitch
                    v-else
                    v-model="slotProps.node.data.operation"
                    :disabled="!selectedMenus[slotProps.node.key]"
                    @update:modelValue="
                      (value) => handleOperationChange(slotProps.node, value)
                    "
                  />
                </template>
              </Column>
            </TreeTable>
          </div>
        </div>
      </Fluid>

      <template #footer>
        <div class="flex justify-content-end">
          <Button
            label="取消"
            icon="pi pi-times"
            text
            @click="roleDrawerVisible = false"
            class="mr-2"
          />
          <Button label="保存" icon="pi pi-check" @click="saveRole" />
        </div>
      </template>
    </Drawer>

    <!-- 菜单配置弹框 -->
    <Dialog
      v-model:visible="dialogVisible"
      v-model:header="headerValue"
      :style="{ width: '20vw' }"
      maximizable
      modal
    >
      <TreeTable
        :value="roleMenus"
        size="large"
        stripedRows
        scrollHeight="flex"
      >
        <Column field="menu_name" header="菜单" expander>
          <template #body="slotProps">
            <div>
              <i :class="slotProps.node.data.icon" class="mr-2"></i>
              <span>{{ slotProps.node.data.menu_name }}</span>
            </div>
          </template>
        </Column>
        <Column field="operation" header="操作权限">
          <template #body="slotProps">
            <ToggleSwitch
              v-model="slotProps.node.data.operation"
              :disabled="true"
            />
          </template>
        </Column>
      </TreeTable>
    </Dialog>
  </div>
</template>

<style scoped>
.role-list-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--p-primary-color);
}

.field label.required::after {
  content: " *";
  color: var(--p-red-500);
}

:deep(.p-drawer-header) {
  padding: 1rem;
  border-bottom: 1px solid var(--p-surface-border);
}

:deep(.p-drawer-content) {
  padding: 0;
}

:deep(.p-drawer-footer) {
  padding: 1rem;
  border-top: 1px solid var(--p-surface-border);
}

:deep(.p-treetable) {
  border: 1px solid var(--p-surface-border);
  border-radius: 6px;
}

:deep(.p-treetable .p-treetable-tbody > tr > td) {
  padding: 0.75rem;
}

:deep(.p-treetable .p-treetable-thead > tr > th) {
  padding: 0.75rem;
  background: var(--p-surface-section);
  color: var(--p-text-color);
  font-weight: 600;
}

:deep(.p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler) {
  margin-right: 0.5rem;
}

:deep(.p-treetable .p-treetable-tbody > tr > td i) {
  font-size: 1rem;
  color: var(--p-primary-color);
}

:deep(.p-treetable .p-treetable-tbody > tr > td .p-togglebutton) {
  width: 3rem;
}

.mr-2 {
  margin-right: 0.5rem;
  color: var(--p-primary-color);
}

.role-form-content {
  padding: 1rem;
}

.w-full {
  width: 100%;
}

:deep(.p-tree) {
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  padding: 1rem;
}

:deep(.p-tree .p-tree-toggler) {
  margin-right: 0.5rem;
}

:deep(.p-tree .p-tree-toggler-icon) {
  font-size: 0.875rem;
}

:deep(.p-tree .p-treenode-content) {
  padding: 0.5rem;
}

:deep(.p-tree .p-treenode-content:hover) {
  background: var(--surface-hover);
}

:deep(.p-tree .p-treenode-content i) {
  font-size: 1rem;
  color: var(--primary-color);
}

:deep(.p-error) {
  padding: 0.5rem;
  color: var(--p-red-500);
}
</style>
