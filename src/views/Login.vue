<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import { login } from '../services/auth';

const router = useRouter();
const toast = useToast();

const email = ref('');
const password = ref('');
const loading = ref(false);
const emailError = ref('');
const passwordError = ref('');

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email.value) {
    emailError.value = '请输入邮箱地址';
    return false;
  }
  if (!emailRegex.test(email.value)) {
    emailError.value = '请输入有效的邮箱地址';
    return false;
  }
  emailError.value = '';
  return true;
};

const validatePassword = () => {
  if (!password.value) {
    passwordError.value = '请输入密码';
    return false;
  }
  if (password.value.length < 6) {
    passwordError.value = '密码长度至少为6位';
    return false;
  }
  passwordError.value = '';
  return true;
};

const passwordStrength = computed(() => {
  if (!password.value) return 0;
  let strength = 0;
  if (password.value.length >= 8) strength++;
  if (/[A-Z]/.test(password.value)) strength++;
  if (/[a-z]/.test(password.value)) strength++;
  if (/[0-9]/.test(password.value)) strength++;
  if (/[^A-Za-z0-9]/.test(password.value)) strength++;
  return strength;
});

const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength <= 1) return 'weak';
  if (strength <= 3) return 'medium';
  return 'strong';
});

const handleLogin = async () => {
  const isEmailValid = validateEmail();
  const isPasswordValid = validatePassword();
  
  if (!isEmailValid || !isPasswordValid) {
    return;
  }
  
  try {
    loading.value = true;
    const response = await login(email.value, password.value);
    
    if (response.code === 200) {
      toast.add({ severity: 'success', summary: '成功', detail: '登录成功', life: 3000 });
      router.push('/');
    } else {
      toast.add({ severity: 'error', summary: '错误', detail: response.message, life: 3000 });
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: '错误', detail: '登录失败，请稍后重试', life: 3000 });
  } finally {
    loading.value = false;
  }
};

const goToForgotPassword = () => {
  router.push('/forgot-password');
};
</script>

<template>
  <div class="login-container">
    <Card>
      <template #title>
        用户登录
      </template>
      <template #content>
        <form @submit.prevent="handleLogin">
          <div class="form-field">
            <label for="email">邮箱</label>
            <InputText 
              id="email" 
              v-model="email" 
              type="email" 
              placeholder="请输入邮箱"
              @blur="validateEmail"
              :class="{ 'p-invalid': emailError }"
              required
            />
            <small class="error-message" v-if="emailError">{{ emailError }}</small>
          </div>
          
          <div class="form-field">
            <label for="password">密码</label>
            <Password 
              id="password" 
              v-model="password" 
              placeholder="请输入密码"
              :feedback="false"
              toggleMask
              @blur="validatePassword"
              :class="{ 'p-invalid': passwordError }"
              required
            />
            <small class="error-message" v-if="passwordError">{{ passwordError }}</small>
            <div class="password-strength" v-if="password">
              <div class="strength-indicator" :class="strengthClass"></div>
            </div>
          </div>

          <div class="form-actions">
            <Button 
              type="button"
              label="忘记密码" 
              link 
              @click="goToForgotPassword"
            />
            <Button 
              type="submit" 
              label="登录" 
              :loading="loading"
              severity="primary"
            />
          </div>
        </form>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.p-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-field label {
  display: block;
  margin-bottom: 0.5rem;
  color: #344767;
  font-weight: 500;
}

.form-field :deep(.p-inputtext),
.form-field :deep(.p-password) {
  width: 100%;
}

.form-field :deep(.p-inputtext:enabled:hover) {
  border-color: #6366f1;
}

.form-field :deep(.p-inputtext:enabled:focus) {
  border-color: #6366f1;
  box-shadow: 0 0 0 1px #6366f1;
}

.error-message {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.password-strength {
  margin-top: 0.5rem;
  height: 4px;
  border-radius: 2px;
  background-color: #e5e7eb;
  overflow: hidden;
}

.strength-indicator {
  height: 100%;
  transition: width 0.3s ease;
}

.strength-indicator.weak {
  width: 33.33%;
  background-color: #ef4444;
}

.strength-indicator.medium {
  width: 66.66%;
  background-color: #f59e0b;
}

.strength-indicator.strong {
  width: 100%;
  background-color: #22c55e;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.form-actions :deep(.p-button) {
  transition: all 0.3s ease;
}

.form-actions :deep(.p-button:not(.p-button-link):hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>