<script setup lang="ts">
import { ref, watch } from "vue";
import type { CustomersInfoItem } from "../../types/customer";
import { formatDateTime } from "../../utils/common";
import {
  customerStateSeverityMap,
  customerApproveStateSeverityMap,
  customerApproveStateValueMap,
} from "../../utils/const";

const props = defineProps<{
  visible: boolean;
  customer: CustomersInfoItem | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const dialogVisible = ref(false);

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
  }
);

// 监听内部状态变化
watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
});

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    modal
    :header="`客户详情 - ${customer?.customer_name || ''}`"
    :style="{ width: '80rem' }"
    class="apple-customer-detail-dialog"
    :closable="true"
    :dismissableMask="true"
    @hide="closeDialog"
    maximizable
  >
    <div v-if="customer" class="customer-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-info-circle"></i>
          基本信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>客户编号</label>
              <span class="detail-value">{{ customer.customer_num }}</span>
            </div>
            <div class="detail-item">
              <label>客户全称</label>
              <span class="detail-value">{{ customer.customer_name }}</span>
            </div>
            <div class="detail-item">
              <label>英文名称</label>
              <span class="detail-value">{{
                customer.customer_name_intl || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>客户简称</label>
              <span class="detail-value">{{ customer.like_key }}</span>
            </div>
            <div class="detail-item">
              <label>所属集团</label>
              <span class="detail-value">{{ customer.group_name }}</span>
            </div>
            <div class="detail-item">
              <label>统一社会信用代码</label>
              <span class="detail-value">{{
                customer.customer_usci || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 分类信息 -->

      <!-- 分类信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-tags"></i>
          分类信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>客户类别</label>
              <span class="detail-value">{{
                customer.customer_class || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>客户类型</label>
              <span class="detail-value">{{
                customer.customer_type || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>行业类型</label>
              <span class="detail-value">{{
                customer.trade_type || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>归属业务</label>
              <span class="detail-value">{{ customer.business || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>执行销售</label>
              <span class="detail-value">{{ customer.sale_name }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 地址信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-map-marker"></i>
          地址信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>国家</label>
              <span class="detail-value">{{ customer.country || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>省份</label>
              <span class="detail-value">{{ customer.province || "--" }}</span>
            </div>
            <div class="detail-item">
              <label>城市</label>
              <span class="detail-value">{{ customer.city || "--" }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 状态信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-flag"></i>
          状态信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>客户状态</label>
              <Tag
                :severity="customerStateSeverityMap[customer.state] || 'info'"
                :value="customer.state || '--'"
                class="detail-tag"
              />
            </div>
            <div class="detail-item">
              <label>审批状态</label>
              <Tag
                :severity="
                  customerApproveStateSeverityMap[customer.approve_state] ||
                  'info'
                "
                :value="customerApproveStateValueMap[customer.approve_state] || '--'"
                class="detail-tag"
              />
            </div>
            <div class="detail-item">
              <label>审批人</label>
              <span class="detail-value">{{
                customer.approve_user || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 其他信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-hashtag"></i>
          其他信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>创建人</label>
              <span class="detail-value">{{ customer.create_user }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间</label>
              <span class="detail-value">{{
                formatDateTime(customer.created_at)
              }}</span>
            </div>
            <div class="detail-item">
              <label>备注信息</label>
              <span class="detail-value">{{ customer.remark || "--" }}</span>
            </div>
            <div class="detail-item" v-if="customer.upload_file">
              <label>附件文件</label>
              <span class="detail-value">{{ customer.upload_file }}</span>
            </div>
          </div>
        </Fluid>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button
          label="关闭"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="closeDialog"
          class="apple-button"
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
/* Apple Design System - 客户详情弹框样式 */
:deep(.apple-customer-detail-dialog) {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
}

:deep(.apple-customer-detail-dialog .p-dialog-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
}

:deep(.apple-customer-detail-dialog .p-dialog-title) {
  font-weight: 600 !important;
  font-size: 1.25rem !important;
  color: #1d1d1f !important;
}

:deep(.apple-customer-detail-dialog .p-dialog-content) {
  padding: 2rem !important;
  background: #ffffff !important;
}

:deep(.apple-customer-detail-dialog .p-dialog-footer) {
  background: #f8f9fa !important;
  border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
}

.customer-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--p-primary-color);
}

.section-title i {
  color: var(--p-primary-color);
  font-size: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #6e6e73;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 1rem;
  color: #1d1d1f;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.detail-tag {
  align-self: flex-start;
  font-weight: 500 !important;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.apple-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.apple-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 滚动条样式 */
.customer-detail-content::-webkit-scrollbar {
  width: 6px;
}

.customer-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.customer-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.customer-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
