<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { getCustomerApproveHistory, ApproveHistoryParams } from "../../services/customer";
import type { CustomerApproveHistoryItem } from "../../types/customer";
import { customerApproveActionLabels, customerApproveStateValueMap } from "../../utils/const";
import { formatDateTime } from "../../utils/common";

const props = defineProps<{
  customerId: number;
  customerName: string;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
}>();

const loading = ref(false);
const histories = ref<CustomerApproveHistoryItem[]>([]);
const totalRecords = ref(0);

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 10,
});

// 加载审批历史记录
const loadApproveHistory = async () => {
  if (!props.customerId) return;
  
  try {
    loading.value = true;
    const params: ApproveHistoryParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    const response = await getCustomerApproveHistory(props.customerId, params);
    histories.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    console.error("加载审批历史记录失败", error);
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadApproveHistory();
};

// 获取操作名称
const getActionName = (action: string) => {
  return customerApproveActionLabels[action as keyof typeof customerApproveActionLabels] || action;
};

// 获取状态名称
const getStateName = (state: string) => {
  return customerApproveStateValueMap[state] || state;
};

// 关闭抽屉
const closeDrawer = () => {
  console.log('关闭抽屉被调用');
  emit('update:visible', false);
  emit('close');
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.customerId) {
    loadApproveHistory();
  }
});

// 监听customerId变化
watch(() => props.customerId, (newVal) => {
  if (newVal && props.visible) {
    loadApproveHistory();
  }
});

// 初始化
onMounted(() => {
  if (props.visible && props.customerId) {
    loadApproveHistory();
  }
});
</script>

<template>
  <Drawer
    :visible="props.visible"
    @update:visible="emit('update:visible', $event)"
    position="right"
    :style="{ width: '80rem' }"
    :modal="true"
    :closable="true"
    :dismissable="true"
    :showCloseIcon="true"
    header="审批历史记录"
    class="p-fluid"
    @hide="closeDrawer"
  >
    <DataTable
      :value="histories"
      :lazy="true"
      :paginator="true"
      :rows="10"
      :rowsPerPageOptions="[10, 20, 50]"
      :totalRecords="totalRecords"
      :loading="loading"
      @page="onPage($event)"
      stripedRows
      showGridlines
      scrollable
      scrollHeight="calc(100vh - 15rem)"
      dataKey="created_at"
    >
      <template #empty>
        <div class="empty-message">
          <i
            class="pi pi-inbox"
            style="
              font-size: 2rem;
              color: var(--p-text-color-secondary);
              margin-bottom: 1rem;
            "
          ></i>
          <p>暂无审批历史记录</p>
        </div>
      </template>
      <div class="flex justify-between items-center mb-3">
        <Message severity="success">
          {{ props.customerName }}
        </Message>
      </div>
      <Column field="action" header="操作类型" style="min-width: 10rem">
        <template #body="{ data }">
          {{ getActionName(data.action) }}
        </template>
      </Column>
      <Column field="from_state" header="原状态" style="min-width: 10rem">
        <template #body="{ data }">
          {{ getStateName(data.from_state) }}
        </template>
      </Column>
      <Column field="to_state" header="目标状态" style="min-width: 10rem">
        <template #body="{ data }">
          {{ getStateName(data.to_state) }}
        </template>
      </Column>
      <Column field="reason" header="原因" style="min-width: 15rem">
        <template #body="{ data }">
          {{ data.reason || '--' }}
        </template>
      </Column>
      <Column field="operator" header="操作人" style="min-width: 10rem" />
      <Column field="created_at" header="操作时间" style="min-width: 15rem">
        <template #body="{ data }">
          {{ formatDateTime(data.created_at) }}
        </template>
      </Column>
    </DataTable>
    <template #footer>
      <Button
        label="关闭"
        icon="pi pi-times"
        @click="closeDrawer"
        class="p-button-text"
      />
    </template>
  </Drawer>
</template>

<style scoped>
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}
</style> 