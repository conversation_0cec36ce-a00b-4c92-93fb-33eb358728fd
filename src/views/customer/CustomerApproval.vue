<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import {
  getCustomersInfoApprove,
  approveCustomer,
  CustomersInfoParams,
} from "../../services/customer";
import type { CustomersInfoItem } from "../../types/customer";
import { customerApproveActionTypes } from "../../utils/const";
import { formatDateTime } from "../../utils/common";
import CustomerDetail from "./CustomerDetail.vue";

const toast = useToast();
const loading = ref(false);
const customers = ref<CustomersInfoItem[]>([]);
const totalRecords = ref(0);

// 视图类型：table(表格视图) 或 card(卡片视图)
const viewType = ref<"table" | "card">("table");

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 10, // 每页显示10条
});

// 切换视图类型
const toggleViewType = () => {
  viewType.value = viewType.value === "table" ? "card" : "table";
  // 如果切换到卡片视图，调整每页数量
  if (viewType.value === "card") {
    lazyParams.value.pageSize = 12;
  } else {
    lazyParams.value.pageSize = 10;
  }
  loadPendingApprovals();
};

// 加载待审批的客户列表
const loadPendingApprovals = async () => {
  try {
    loading.value = true;
    const params: CustomersInfoParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    const response = await getCustomersInfoApprove(params);
    customers.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载待审批客户列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件，修复事件参数解析
const onPage = (event: any) => {
  // PrimeFaces Paginator 组件的事件结构是 {first, rows, page, pageCount}
  // 其中 page 是从 0 开始的，所以需要 +1
  console.log("分页事件:", event);
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadPendingApprovals();
};

// 审批对话框相关
const approveDialogVisible = ref(false);
const rejectDialogVisible = ref(false);
const selectedCustomerId = ref<number | null>(null);
const selectedCustomerName = ref<string>("");
const rejectReason = ref("");

// 客户详情相关
const customerDetailVisible = ref(false);
const selectedCustomerForDetail = ref<CustomersInfoItem | null>(null);

// 打开客户详情弹框
const openCustomerDetail = (customer: CustomersInfoItem) => {
  selectedCustomerForDetail.value = customer;
  customerDetailVisible.value = true;
};

// 打开审批通过对话框
const openApproveDialog = (customer: CustomersInfoItem) => {
  selectedCustomerId.value = customer.id!;
  selectedCustomerName.value = customer.customer_name;
  approveDialogVisible.value = true;
};

// 打开审批驳回对话框
const openRejectDialog = (customer: CustomersInfoItem) => {
  selectedCustomerId.value = customer.id!;
  selectedCustomerName.value = customer.customer_name;
  rejectReason.value = "";
  rejectDialogVisible.value = true;
};

// 执行审批通过操作
const handleApprove = async () => {
  if (!selectedCustomerId.value) return;

  try {
    await approveCustomer({
      id: selectedCustomerId.value,
      action: customerApproveActionTypes.APPROVE,
    });

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "审批通过成功",
      life: 3000,
    });

    approveDialogVisible.value = false;
    loadPendingApprovals(); // 刷新列表
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "审批通过失败",
      life: 3000,
    });
  }
};

// 执行审批驳回操作
const handleReject = async () => {
  if (!selectedCustomerId.value) return;

  try {
    await approveCustomer({
      id: selectedCustomerId.value,
      action: customerApproveActionTypes.REJECT,
      reason: rejectReason.value || undefined,
    });

    toast.add({
      severity: "success",
      summary: "成功",
      detail: "审批驳回成功",
      life: 3000,
    });

    rejectDialogVisible.value = false;
    loadPendingApprovals(); // 刷新列表
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "审批驳回失败",
      life: 3000,
    });
  }
};

// 初始化
onMounted(() => {
  loadPendingApprovals();
});
</script>

<template>
  <div class="customer-approval-container">
    <Toast />
    <div class="card">
      <div class="card-header">
        <Message variant="simple" size="large">客户审批</Message>
        <div class="view-toggle">
          <Button
            :icon="viewType === 'table' ? 'pi pi-th-large' : 'pi pi-list'"
            :label="viewType === 'table' ? '卡片视图' : '表格视图'"
            severity="secondary"
            outlined
            @click="toggleViewType"
            class="toggle-button"
          />
        </div>
      </div>

      <!-- 加载状态 -->
      <ProgressSpinner v-if="loading" class="loading-spinner" />

      <!-- 空数据状态 -->
      <div v-else-if="customers.length === 0" class="empty-state">
        <i class="pi pi-inbox empty-icon"></i>
        <p>暂无待审批的客户信息</p>
      </div>

      <!-- 表格视图 -->
      <div v-else-if="viewType === 'table'" class="table-view">
        <DataTable
          :value="customers"
          :rowHover="true"
          :lazy="true"
          :loading="loading"
          stripedRows
          showGridlines
          scrollable
          scrollHeight="calc(100vh - 22rem)"
        >
          <Column
            field="customer_num"
            header="客户编号"
            style="min-width: 15rem"
          />
          <Column
            field="customer_name"
            header="客户名称"
            style="min-width: 25rem"
          />
          <Column
            field="customer_class"
            header="客户类别"
            style="min-width: 8rem"
          />
          <Column field="create_user" header="创建人" style="min-width: 8rem" />
          <Column field="created_at" header="创建时间" style="min-width: 15rem">
            <template #body="{ data }">
              <div>{{ formatDateTime(data.created_at) }}</div>
            </template>
          </Column>
          <Column
            header="操作"
            style="min-width: 12rem"
            alignFrozen="right"
            frozen
          >
            <template #body="{ data }">
              <div class="action-buttons">
                <Button
                  icon="pi pi-eye"
                  severity="info"
                  outlined
                  rounded
                  v-tooltip.top="'查看客户详情'"
                  @click="openCustomerDetail(data)"
                  class="p-button-sm"
                />
                <Button
                  icon="pi pi-check"
                  severity="success"
                  outlined
                  rounded
                  v-tooltip.top="'审批通过'"
                  @click="openApproveDialog(data)"
                  class="p-button-sm ml-2"
                />
                <Button
                  icon="pi pi-times"
                  severity="danger"
                  outlined
                  rounded
                  v-tooltip.top="'审批驳回'"
                  @click="openRejectDialog(data)"
                  class="p-button-sm ml-2"
                />
              </div>
            </template>
          </Column>
        </DataTable>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <Fluid>
          <div class="grid grid-cols-6 gap-3">
            <div v-for="customer in customers" :key="customer.id">
              <Card class="customer-card">
                <template #header>
                  <div class="customer-card-header">
                    <Button
                      icon="pi pi-eye"
                      severity="info"
                      outlined
                      rounded
                      size="small"
                      v-tooltip.top="'查看客户详情'"
                      @click="openCustomerDetail(customer)"
                      class="detail-button"
                    />
                    <Tag severity="help" value="待审批" />
                  </div>
                </template>
                <template #title>
                  <div class="customer-card-title">
                    {{ customer.customer_name }}
                  </div>
                </template>
                <template #subtitle>
                  <div class="customer-card-subtitle">
                    {{ customer.customer_num }}
                  </div>
                </template>
                <template #content>
                  <div class="customer-card-content">
                    <div class="customer-info-item">
                      <span class="info-label">客户类别:</span>
                      <span class="info-value">{{
                        customer.customer_class || "--"
                      }}</span>
                    </div>
                    <div class="customer-info-item">
                      <span class="info-label">创建人:</span>
                      <span class="info-value">{{ customer.create_user }}</span>
                    </div>
                    <div class="customer-info-item">
                      <span class="info-label">创建时间:</span>
                      <span class="info-value">{{
                        formatDateTime(customer.created_at)
                      }}</span>
                    </div>
                  </div>
                </template>
                <template #footer>
                  <div class="customer-card-actions">
                    <Button
                      label="审批通过"
                      icon="pi pi-check"
                      severity="success"
                      @click="openApproveDialog(customer)"
                      class="p-button-sm"
                    />
                    <Button
                      label="审批驳回"
                      icon="pi pi-times"
                      severity="danger"
                      @click="openRejectDialog(customer)"
                      class="p-button-sm ml-2"
                    />
                  </div>
                </template>
              </Card>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 统一的分页组件 -->
      <Paginator
        v-if="customers.length > 0"
        :rows="lazyParams.pageSize"
        :totalRecords="totalRecords"
        :rowsPerPageOptions="viewType === 'table' ? [10, 20, 30] : [12, 24, 36]"
        @page="onPage($event)"
        class="paginator"
      />
    </div>

    <!-- 审批通过确认对话框 -->
    <Dialog
      v-model:visible="approveDialogVisible"
      :style="{ width: '30rem' }"
      header="确认审批通过"
      :modal="true"
      class="approval-dialog"
    >
      <div class="confirmation-message">
        <i class="pi pi-check-circle confirmation-icon success"></i>
        <p>
          确定要审批通过客户 <strong>{{ selectedCustomerName }}</strong> 吗？
        </p>
      </div>
      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          @click="approveDialogVisible = false"
          class="p-button-text"
        />
        <Button
          label="确认通过"
          icon="pi pi-check"
          severity="success"
          @click="handleApprove"
        />
      </template>
    </Dialog>

    <!-- 审批驳回对话框 -->
    <Dialog
      v-model:visible="rejectDialogVisible"
      :style="{ width: '30rem' }"
      header="确认审批驳回"
      :modal="true"
      class="approval-dialog"
    >
      <div class="confirmation-message">
        <i class="pi pi-times-circle confirmation-icon danger"></i>
        <p>
          确定要驳回客户 <strong>{{ selectedCustomerName }}</strong> 吗？
        </p>
      </div>
      <div class="field mt-4">
        <label for="rejectReason">驳回原因</label>
        <Textarea
          id="rejectReason"
          v-model="rejectReason"
          rows="3"
          placeholder="请输入驳回原因（可选）"
          class="w-full"
          autoResize
        />
      </div>
      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          @click="rejectDialogVisible = false"
          class="p-button-text"
        />
        <Button
          label="确认驳回"
          icon="pi pi-check"
          severity="danger"
          @click="handleReject"
        />
      </template>
    </Dialog>

    <!-- 客户详情弹框 -->
    <CustomerDetail
      v-model:visible="customerDetailVisible"
      :customer="selectedCustomerForDetail"
      @update:visible="customerDetailVisible = $event"
    />
  </div>
</template>

<style scoped>
.customer-approval-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.view-toggle {
  display: flex;
  align-items: center;
}

.toggle-button {
  transition: all 0.2s ease;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 2rem 0;
}

.empty-icon {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 1rem;
}

.customer-list {
  margin-bottom: 1rem;
}

.customer-id {
  font-family: "SF Mono", monospace;
  font-size: 0.9rem;
  color: #555;
}

.customer-name {
  font-weight: 500;
  color: #333;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
}

.paginator {
  margin-top: 1rem;
}

.confirmation-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0;
}

.confirmation-message p {
  text-align: center;
  line-height: 1.5;
}

.confirmation-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.confirmation-icon.success {
  color: var(--green-500);
}

.confirmation-icon.danger {
  color: var(--red-500);
}

/* 卡片视图样式 */
.card-view {
  margin-bottom: 1.5rem;
}

.p-fluid {
  height: calc(100vh - 22rem);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.customer-card {
  transition: transform 0.2s, box-shadow 0.2s;
  border-radius: 12px;
  overflow: hidden;
}

.customer-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.customer-card-header {
  padding: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-button {
  width: 2rem !important;
  height: 2rem !important;
  padding: 0 !important;
  border-radius: 50% !important;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.detail-button:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3) !important;
}

.customer-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.customer-card-subtitle {
  color: #666;
  font-size: 0.9rem;
}

.customer-card-content {
  padding: 0.5rem 0;
}

.customer-info-item {
  display: flex;
  margin-bottom: 0.5rem;
}

.info-label {
  font-weight: 500;
  color: #666;
  width: 5rem;
}

.info-value {
  flex: 1;
  color: #333;
}

.customer-card-actions {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

/* 表格视图样式 */
:deep(.p-datatable) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background-color: #f8f8f8;
  color: #333;
  font-weight: 600;
  padding: 0.75rem 1rem;
  border-width: 0 0 1px 0;
  text-align: left;
}

:deep(.p-datatable .p-datatable-tbody > tr) {
  transition: background-color 0.2s;
}

:deep(.p-datatable .p-datatable-tbody > tr:hover) {
  background-color: #f5f9ff;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  padding: 0.75rem 1rem;
  border-width: 0 0 1px 0;
  border-color: #f0f0f0;
}

:deep(.p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th) {
  border-width: 0 0 1px 0;
  border-color: #e0e0e0;
}

:deep(.p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td) {
  border-width: 0 0 1px 0;
  border-color: #f0f0f0;
}

:deep(.p-datatable.p-datatable-striped .p-datatable-tbody > tr.p-row-odd) {
  background-color: #fafafa;
}

:deep(.p-datatable .p-datatable-tbody > tr.p-highlight) {
  background-color: #edf4ff;
  color: #1a73e8;
}

/* 卡片样式 */
:deep(.p-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: none;
}

:deep(.p-card .p-card-title) {
  font-weight: 600;
}

:deep(.p-card .p-card-content) {
  padding: 1rem 0;
}

:deep(.p-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 对话框样式 */
:deep(.p-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.p-dialog .p-dialog-header) {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.p-dialog .p-dialog-content) {
  padding: 1.5rem;
}

:deep(.p-dialog .p-dialog-footer) {
  padding: 1rem 1.5rem;
  border-top: 1px solid #f0f0f0;
}

/* 按钮样式 */
:deep(.p-button) {
  border-radius: 8px;
  transition: all 0.2s ease;
}

:deep(.p-button:active) {
  transform: scale(0.98);
}

:deep(.p-button.p-button-sm) {
  font-size: 0.875rem;
  padding: 0.4rem 0.8rem;
}

:deep(.p-inputtext:focus) {
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.25);
  border-color: #007aff;
}

/* 分页器样式 */
:deep(.p-paginator) {
  background-color: transparent;
  border: none;
  padding: 1rem 0;
}

:deep(.p-paginator .p-paginator-pages .p-paginator-page.p-highlight) {
  background-color: #007aff;
  color: #ffffff;
}

/* 响应式样式 */
@media screen and (max-width: 960px) {
  .action-buttons {
    justify-content: flex-start;
    margin-top: 0.5rem;
  }

  :deep(.p-datatable.p-datatable-responsive .p-datatable-tbody > tr > td) {
    padding: 0.75rem;
  }

  :deep(
      .p-datatable.p-datatable-responsive
        .p-datatable-tbody
        > tr
        > td
        .p-column-title
    ) {
    font-weight: 600;
    color: #555;
    margin-bottom: 0.25rem;
  }
}
</style>
