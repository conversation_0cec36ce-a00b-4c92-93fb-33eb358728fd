<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const toast = useToast()

const email = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const verificationCode = ref('')
const showResetForm = ref(false)
const emailError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')
const verificationCodeError = ref('')

const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!email.value) {
    emailError.value = '请输入邮箱地址'
    return false
  }
  if (!emailRegex.test(email.value)) {
    emailError.value = '请输入有效的邮箱地址'
    return false
  }
  emailError.value = ''
  return true
}

const validatePassword = () => {
  if (!newPassword.value) {
    passwordError.value = '请输入新密码'
    return false
  }
  if (newPassword.value.length < 6) {
    passwordError.value = '密码长度至少为6位'
    return false
  }
  passwordError.value = ''
  return true
}

const validateConfirmPassword = () => {
  if (!confirmPassword.value) {
    confirmPasswordError.value = '请确认新密码'
    return false
  }
  if (confirmPassword.value !== newPassword.value) {
    confirmPasswordError.value = '两次输入的密码不一致'
    return false
  }
  confirmPasswordError.value = ''
  return true
}

const validateVerificationCode = () => {
  if (!verificationCode.value) {
    verificationCodeError.value = '请输入验证码'
    return false
  }
  if (!/^\d{6}$/.test(verificationCode.value)) {
    verificationCodeError.value = '验证码格式不正确'
    return false
  }
  verificationCodeError.value = ''
  return true
}

const passwordStrength = computed(() => {
  if (!newPassword.value) return 0
  let strength = 0
  if (newPassword.value.length >= 8) strength++
  if (/[A-Z]/.test(newPassword.value)) strength++
  if (/[a-z]/.test(newPassword.value)) strength++
  if (/[0-9]/.test(newPassword.value)) strength++
  if (/[^A-Za-z0-9]/.test(newPassword.value)) strength++
  return strength
})

const strengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength <= 1) return 'weak'
  if (strength <= 3) return 'medium'
  return 'strong'
})

const requestReset = () => {
  if (!validateEmail()) {
    return
  }
  
  // TODO: 实现发送验证码逻辑
  toast.add({ severity: 'success', summary: '成功', detail: '验证码已发送到您的邮箱', life: 3000 })
  showResetForm.value = true
}

const resetPassword = () => {
  const isEmailValid = validateEmail()
  const isPasswordValid = validatePassword()
  const isConfirmPasswordValid = validateConfirmPassword()
  const isVerificationCodeValid = validateVerificationCode()

  if (!isEmailValid || !isPasswordValid || !isConfirmPasswordValid || !isVerificationCodeValid) {
    return
  }

  // TODO: 实现重置密码逻辑
  toast.add({ severity: 'success', summary: '成功', detail: '密码重置成功', life: 3000 })
  router.push('/login')
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="forgot-password-container">
    <Card>
      <template #title>
        重置密码
      </template>
      <template #content>
        <form @submit.prevent="showResetForm ? resetPassword() : requestReset()">
          <div class="form-field">
            <label for="email">邮箱</label>
            <InputText 
              id="email" 
              v-model="email" 
              type="email" 
              placeholder="请输入邮箱"
              :disabled="showResetForm"
              @blur="validateEmail"
              :class="{ 'p-invalid': emailError }"
              required
            />
            <small class="error-message" v-if="emailError">{{ emailError }}</small>
          </div>

          <template v-if="showResetForm">
            <div class="form-field">
              <label for="verificationCode">验证码</label>
              <InputText 
                id="verificationCode" 
                v-model="verificationCode" 
                placeholder="请输入验证码"
                @blur="validateVerificationCode"
                :class="{ 'p-invalid': verificationCodeError }"
                required
              />
              <small class="error-message" v-if="verificationCodeError">{{ verificationCodeError }}</small>
            </div>

            <div class="form-field">
              <label for="newPassword">新密码</label>
              <Password 
                id="newPassword" 
                v-model="newPassword" 
                placeholder="请输入新密码"
                @blur="validatePassword"
                :class="{ 'p-invalid': passwordError }"
                required
              />
              <small class="error-message" v-if="passwordError">{{ passwordError }}</small>
              <div class="password-strength" v-if="newPassword">
                <div class="strength-indicator" :class="strengthClass"></div>
              </div>
            </div>

            <div class="form-field">
              <label for="confirmPassword">确认密码</label>
              <Password 
                id="confirmPassword" 
                v-model="confirmPassword" 
                placeholder="请再次输入新密码"
                :feedback="false"
                @blur="validateConfirmPassword"
                :class="{ 'p-invalid': confirmPasswordError }"
                required
              />
              <small class="error-message" v-if="confirmPasswordError">{{ confirmPasswordError }}</small>
            </div>
          </template>

          <div class="form-actions">
            <Button 
              type="button" 
              label="返回登录" 
              link 
              @click="goToLogin"
            />
            <Button 
              type="submit" 
              :label="showResetForm ? '重置密码' : '发送验证码'" 
              severity="primary"
            />
          </div>
        </form>
      </template>
    </Card>
  </div>
</template>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.p-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-field label {
  display: block;
  margin-bottom: 0.5rem;
  color: #344767;
  font-weight: 500;
}

.form-field :deep(.p-inputtext),
.form-field :deep(.p-password) {
  width: 100%;
}

.form-field :deep(.p-inputtext:enabled:hover) {
  border-color: #6366f1;
}

.form-field :deep(.p-inputtext:enabled:focus) {
  border-color: #6366f1;
  box-shadow: 0 0 0 1px #6366f1;
}

.error-message {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.password-strength {
  margin-top: 0.5rem;
  height: 4px;
  border-radius: 2px;
  background-color: #e5e7eb;
  overflow: hidden;
}

.strength-indicator {
  height: 100%;
  transition: width 0.3s ease;
}

.strength-indicator.weak {
  width: 33.33%;
  background-color: #ef4444;
}

.strength-indicator.medium {
  width: 66.66%;
  background-color: #f59e0b;
}

.strength-indicator.strong {
  width: 100%;
  background-color: #22c55e;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.form-actions :deep(.p-button) {
  transition: all 0.3s ease;
}

.form-actions :deep(.p-button:not(.p-button-link):hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>