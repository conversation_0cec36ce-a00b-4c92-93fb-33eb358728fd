# 银行流水管理页面

## 功能概述

银行流水管理页面（`bankFlow.vue`）用于管理和编辑银行流水记录。

## 主要功能

### 1. 列表展示
- 显示银行流水列表，包含以下字段：
  - 流水号
  - 金额（支持多币种显示）
  - 流水日期
  - 付款方名称
  - 付款银行
  - 收款银行
  - 状态（待认款、待确认、已确认）
  - 创建时间

### 2. 筛选功能
- **流水状态筛选**：支持按状态筛选（待认款-0、待确认-1、已确认-2）
- **流水号筛选**：支持按流水号模糊搜索
- **付款方名称筛选**：支持按付款方名称模糊搜索
- **重置功能**：一键清空所有筛选条件

### 3. 分页功能
- 支持分页显示，默认每页20条记录
- 可选择每页显示10、20、50条记录

### 4. 编辑功能
- 点击编辑按钮打开抽屉式编辑表单
- 支持编辑以下字段：
  - 金额（必填）
  - 货币类型（必填，下拉选择）
  - 流水号（必填）
  - 流水日期（必填，日期选择器）
  - 付款方名称（必填）
  - 付款银行账号
  - 付款银行名称
  - 收款银行名称
  - 收款银行账号
  - 待确认金额
  - 已确认金额
  - 客户编号
  - 客户名称
  - 备注

### 5. 表单验证
- **前端验证**：必填字段验证
- **后端验证**：支持422状态码的字段级错误处理
- **错误提示**：显示具体的字段错误信息

## API接口

### 获取银行流水列表
- **接口**：`GET /bank-statements`
- **参数**：
  - `page`: 页码
  - `pageSize`: 每页数量
  - `state`: 流水状态（可选）
  - `statement_no`: 流水号（可选）
  - `payment_name`: 付款方名称（可选）

### 更新银行流水
- **接口**：`PUT /bank-statements/{id}`
- **参数**：银行流水表单数据

## 状态说明

| 状态值 | 状态名称 | 颜色标识 |
|--------|----------|----------|
| 0      | 待认款   | 警告色   |
| 1      | 待确认   | 信息色   |
| 2      | 已确认   | 成功色   |

## 路由配置

页面路由：`/receipt-management/bank-flow`

## 权限控制

- 编辑操作需要相应的操作权限
- 使用 `usePermission` 组合式函数进行权限检查

## 技术特性

- 使用 Vue 3 Composition API
- 集成 PrimeVue UI 组件库
- 支持响应式设计
- 遵循 Apple 设计规范
- 支持多币种金额格式化显示
- 抽屉式编辑界面，用户体验友好
