<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useToast } from "primevue/usetoast";
import {
  getBankStatementDetail,
  getReceiveStatements,
  getRecognitionDetails,
} from "../../services/bankStatement";
import type {
  BankStatementItem,
  ReceiveStatementItem,
  RecognitionDetailItem,
  RecognitionDetailParams,
} from "../../types/bankStatement";
import { formatDateTime, formatCurrency } from "../../utils/common";

// Props
const props = defineProps<{
  bankStatementId: number;
}>();

const toast = useToast();

// 银行流水详情
const bankStatementDetail = ref<BankStatementItem | null>(null);
const loadingDetail = ref(false);

// 右侧Tab管理
const activeRightTab = ref("received");

// 已认款数据
const receiveStatements = ref<ReceiveStatementItem[]>([]);
const receiveLoading = ref(false);
const receiveTotalRecords = ref(0);
const receiveLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 待认款数据
const recognitionDetails = ref<RecognitionDetailItem[]>([]);
const selectedRecognitionDetails = ref<RecognitionDetailItem[]>([]);
const recognitionLoading = ref(false);
const recognitionTotalRecords = ref(0);
const recognitionLazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 待认款筛选参数
const recognitionFilters = ref({
  account_seq: "",
  adjust_month: "",
  charge_month: "",
  order_no: "",
});

// 认款金额输入框数据
const recognitionAmounts = ref<Record<number, number>>({});

// 计算当前认款总金额
const currentRecognitionAmount = computed(() => {
  return selectedRecognitionDetails.value.reduce((total, item) => {
    const amount = recognitionAmounts.value[item.id] || 0;
    return total + amount;
  }, 0);
});

// 计算剩余金额
const remainingAmount = computed(() => {
  if (!bankStatementDetail.value) return 0;
  const totalAmount = parseFloat(bankStatementDetail.value.amount) || 0;
  return totalAmount - currentRecognitionAmount.value;
});

// 计算进度百分比
const progressPercentage = computed(() => {
  if (!bankStatementDetail.value) return 0;
  const totalAmount = parseFloat(bankStatementDetail.value.amount) || 0;
  if (totalAmount === 0) return 0;
  return Math.min((currentRecognitionAmount.value / totalAmount) * 100, 100);
});

// 监听选中项变化，初始化输入框值
watch(
  selectedRecognitionDetails,
  (newSelection) => {
    newSelection.forEach((item) => {
      if (!(item.id in recognitionAmounts.value)) {
        recognitionAmounts.value[item.id] = parseFloat(item.fee_amount) || 0;
      }
    });

    // 清理未选中项的输入值
    const selectedIds = new Set(newSelection.map((item) => item.id));
    Object.keys(recognitionAmounts.value).forEach((id) => {
      if (!selectedIds.has(parseInt(id))) {
        delete recognitionAmounts.value[parseInt(id)];
      }
    });
  },
  { deep: true }
);

// 加载银行流水详情
const loadBankStatementDetail = async () => {
  loadingDetail.value = true;
  try {
    const response = await getBankStatementDetail(props.bankStatementId);
    bankStatementDetail.value = response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载银行流水详情失败",
      life: 3000,
    });
  } finally {
    loadingDetail.value = false;
  }
};

// 加载已认款列表
const loadReceiveStatements = async () => {
  receiveLoading.value = true;
  try {
    const response = await getReceiveStatements(props.bankStatementId, {
      page: receiveLazyParams.value.page,
      pageSize: receiveLazyParams.value.pageSize,
    });
    receiveStatements.value = response.data.records;
    receiveTotalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载已认款列表失败",
      life: 3000,
    });
  } finally {
    receiveLoading.value = false;
  }
};

// 加载待认款列表
const loadRecognitionDetails = async () => {
  recognitionLoading.value = true;
  try {
    const params: RecognitionDetailParams = {
      page: recognitionLazyParams.value.page,
      pageSize: recognitionLazyParams.value.pageSize,
    };

    // 添加筛选参数
    if (recognitionFilters.value.account_seq.trim()) {
      params.account_seq = recognitionFilters.value.account_seq.trim();
    }
    if (recognitionFilters.value.adjust_month.trim()) {
      params.adjust_month = recognitionFilters.value.adjust_month.trim();
    }
    if (recognitionFilters.value.charge_month.trim()) {
      params.charge_month = recognitionFilters.value.charge_month.trim();
    }
    if (recognitionFilters.value.order_no.trim()) {
      params.order_no = recognitionFilters.value.order_no.trim();
    }

    const response = await getRecognitionDetails(props.bankStatementId, params);
    recognitionDetails.value = response.data.records;
    recognitionTotalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载待认款列表失败",
      life: 3000,
    });
  } finally {
    recognitionLoading.value = false;
  }
};

// 处理已认款分页
const onReceivePage = (event: { page: number; rows: number }) => {
  receiveLazyParams.value.page = event.page + 1;
  receiveLazyParams.value.pageSize = event.rows;
  loadReceiveStatements();
};

// 处理待认款分页
const onRecognitionPage = (event: { page: number; rows: number }) => {
  recognitionLazyParams.value.page = event.page + 1;
  recognitionLazyParams.value.pageSize = event.rows;
  loadRecognitionDetails();
};

// 搜索待认款
const handleRecognitionSearch = () => {
  recognitionLazyParams.value.page = 1;
  loadRecognitionDetails();
};

// 重置待认款筛选
const resetRecognitionFilters = () => {
  recognitionFilters.value = {
    account_seq: "",
    adjust_month: "",
    charge_month: "",
    order_no: "",
  };
  recognitionLazyParams.value.page = 1;
  loadRecognitionDetails();
};

// 格式化日期为yyyymm格式
const formatDateToYYYYMM = (date: Date | null): string => {
  if (!date) return "";
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `${year}${month}`;
};

// 日期变化处理
const onAdjustMonthChange = (date: Date | null) => {
  recognitionFilters.value.adjust_month = formatDateToYYYYMM(date);
};

const onChargeMonthChange = (date: Date | null) => {
  recognitionFilters.value.charge_month = formatDateToYYYYMM(date);
};

onMounted(() => {
  loadBankStatementDetail();
  loadReceiveStatements();
  loadRecognitionDetails();
});
</script>

<template>
  <div class="recognition-detail-container">
    <!-- 分栏布局 -->
    <div class="split-layout">
      <!-- 左侧：银行流水详情 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <i class="pi pi-info-circle"></i>
            银行流水详情
          </h3>
        </div>
        <div class="panel-content" v-if="bankStatementDetail">
          <Fluid>
            <div class="grid grid-cols-1 gap-5">
              <div class="detail-item">
                <label>流水号</label>
                <span>{{ bankStatementDetail.statement_no }}</span>
              </div>
              <div class="detail-item">
                <label>金额</label>
                <span>{{
                  formatCurrency(
                    bankStatementDetail.amount,
                    bankStatementDetail.currency_type
                  )
                }}</span>
              </div>
              <div class="detail-item">
                <label>交易日期</label>
                <span>{{ bankStatementDetail.statement_date }}</span>
              </div>
              <div class="detail-item">
                <label>付款方名称</label>
                <span>{{ bankStatementDetail.payment_name }}</span>
              </div>
              <div class="detail-item">
                <label>付款银行</label>
                <span>{{ bankStatementDetail.payment_bank_name }}</span>
              </div>
              <div class="detail-item">
                <label>付款账号</label>
                <span>{{ bankStatementDetail.payment_bank_no }}</span>
              </div>
              <div class="detail-item">
                <label>收款银行</label>
                <span>{{ bankStatementDetail.receive_bank_name }}</span>
              </div>
              <div class="detail-item">
                <label>收款账号</label>
                <span>{{ bankStatementDetail.receive_bank_no }}</span>
              </div>
              <div class="detail-item">
                <label>客户名称</label>
                <span>{{ bankStatementDetail.customer_name || "--" }}</span>
              </div>
              <div
                class="detail-item full-width"
                v-if="bankStatementDetail.description"
              >
                <label>摘要</label>
                <span>{{ bankStatementDetail.description }}</span>
              </div>
            </div>
            <!-- 核销金额统计区域 -->
            <div class="recognition-summary" v-if="bankStatementDetail">
              <div class="summary-header">
                <h4 class="summary-title">
                  <i class="pi pi-calculator"></i>
                  核销金额统计
                </h4>
              </div>
              <div class="summary-content">
                <div class="summary-item">
                  <label>银行流水总金额</label>
                  <span class="amount-value total-amount">
                    {{
                      formatCurrency(
                        bankStatementDetail.amount,
                        bankStatementDetail.currency_type
                      )
                    }}
                  </span>
                </div>
                <div class="summary-item">
                  <label>当前认款金额</label>
                  <span class="amount-value current-amount">
                    {{
                      formatCurrency(
                        currentRecognitionAmount.toString(),
                        bankStatementDetail.currency_type
                      )
                    }}
                  </span>
                </div>
                <div class="summary-item">
                  <label>剩余金额</label>
                  <span class="amount-value remaining-amount">
                    {{
                      formatCurrency(
                        remainingAmount.toString(),
                        bankStatementDetail.currency_type
                      )
                    }}
                  </span>
                </div>
                <div class="progress-section">
                  <div class="progress-header">
                    <span class="progress-label">核销进度</span>
                    <span class="progress-percentage"
                      >{{ progressPercentage.toFixed(1) }}%</span
                    >
                  </div>
                  <ProgressBar
                    class="recognition-progress"
                    :class="{ 'progress-complete': progressPercentage >= 100 }"
                  />
                </div>
              </div>
            </div>
          </Fluid>
        </div>
        <div class="panel-content" v-else>
          <div class="loading-placeholder">
            <ProgressSpinner v-if="loadingDetail" />
            <p v-else>暂无详情数据</p>
          </div>
        </div>
      </div>

      <!-- 右侧：认款信息Tab -->
      <div class="right-panel">
        <Tabs
          :value="activeRightTab"
          @update:modelValue="activeRightTab = $event"
          class="recognition-tabs"
        >
          <TabList>
            <Tab value="received">已认款</Tab>
            <Tab value="pending">待认款</Tab>
          </TabList>
          <TabPanels>
            <!-- 已认款Tab -->
            <TabPanel value="received">
              <DataTable
                :value="receiveStatements"
                :lazy="true"
                :paginator="true"
                :rows="20"
                :rowsPerPageOptions="[10, 20, 50]"
                :totalRecords="receiveTotalRecords"
                :loading="receiveLoading"
                @page="onReceivePage($event)"
                class="p-datatable-sm"
                showGridlines
                stripedRows
                scrollable
                scrollHeight="calc(100vh - 35rem)"
              >
                <template #empty>
                  <div class="empty-message">
                    <i
                      class="pi pi-inbox"
                      style="
                        font-size: 2rem;
                        color: var(--p-text-color-secondary);
                        margin-bottom: 1rem;
                      "
                    ></i>
                    <p>暂无已认款数据</p>
                  </div>
                </template>
                <Column field="id" header="ID" style="min-width: 5rem" />
                <Column
                  field="bank_statement_no"
                  header="银行流水号"
                  style="min-width: 15rem"
                />
                <Column
                  field="created_at"
                  header="认款时间"
                  style="min-width: 12rem"
                >
                  <template #body="slotProps">
                    <span>{{ formatDateTime(slotProps.data.created_at) }}</span>
                  </template>
                </Column>
              </DataTable>
            </TabPanel>

            <!-- 待认款Tab -->
            <TabPanel value="pending">
              <!-- 筛选区域 -->
              <Toolbar class="mb-2">
                <template #end>
                  <div class="flex flex-wrap align-items-center gap-2">
                    <FloatLabel>
                      <label for="filterOrderNo">订单编号</label>
                      <InputText
                        id="filterOrderNo"
                        v-model="recognitionFilters.order_no"
                      />
                    </FloatLabel>
                    <FloatLabel>
                      <label for="filterAccountSeq">分账序号</label>
                      <InputText
                        id="filterAccountSeq"
                        v-model="recognitionFilters.account_seq"
                      />
                    </FloatLabel>
                    <FloatLabel>
                      <label for="filterChargeMonth">权责账期</label>
                      <DatePicker
                        id="filterChargeMonth"
                        view="month"
                        dateFormat="yy-mm"
                        @date-select="onChargeMonthChange"
                      />
                    </FloatLabel>
                    <FloatLabel class="mr-2">
                      <label for="filterAdjustMonth">调整账期</label>
                      <DatePicker
                        id="filterAdjustMonth"
                        view="month"
                        dateFormat="yy-mm"
                        @date-select="onAdjustMonthChange"
                      />
                    </FloatLabel>
                  </div>
                  <Button
                    label="搜索"
                    icon="pi pi-search"
                    @click="handleRecognitionSearch"
                    class="mr-2 p-button-sm"
                  />
                  <Button
                    label="重置"
                    icon="pi pi-refresh"
                    @click="resetRecognitionFilters"
                    outlined
                    class="p-button-sm"
                  />
                </template>
              </Toolbar>

              <DataTable
                :value="recognitionDetails"
                v-model:selection="selectedRecognitionDetails"
                :lazy="true"
                :paginator="true"
                :rows="20"
                :rowsPerPageOptions="[10, 20, 50]"
                :totalRecords="recognitionTotalRecords"
                :loading="recognitionLoading"
                @page="onRecognitionPage($event)"
                showGridlines
                stripedRows
                scrollable
                scrollHeight="calc(100vh - 32rem)"
              >
                <template #empty>
                  <div class="empty-message">
                    <i
                      class="pi pi-inbox"
                      style="
                        font-size: 2rem;
                        color: var(--p-text-color-secondary);
                        margin-bottom: 1rem;
                      "
                    ></i>
                    <p>暂无待认款数据</p>
                  </div>
                </template>
                <Column
                  selectionMode="multiple"
                  headerStyle="width: 3rem"
                  align-frozen="left"
                  frozen
                />
                <Column
                  header="核销金额"
                  style="min-width: 12rem"
                  align-frozen="left"
                  frozen
                >
                  <template #body="slotProps">
                    <InputNumber
                      v-model="recognitionAmounts[slotProps.data.id]"
                      :disabled="
                        !selectedRecognitionDetails.some(
                          (item) => item.id === slotProps.data.id
                        )
                      "
                      mode="currency"
                      :currency="slotProps.data.currency_type"
                      locale="zh-CN"
                      :min="0"
                      :max="parseFloat(slotProps.data.fee_amount)"
                      class="recognition-amount-input"
                      size="small"
                    />
                  </template>
                </Column>
                <Column
                  field="order_no"
                  header="订单编号"
                  style="min-width: 15rem"
                />
                <Column
                  field="sub_order_no"
                  header="合成编号"
                  style="min-width: 12rem"
                />
                <Column
                  field="account_seq"
                  header="分账序号"
                  style="min-width: 10rem"
                />
                <Column
                  field="charge_month"
                  header="权责账期"
                  style="min-width: 8rem"
                />
                <Column
                  field="adjust_month"
                  header="调整账期"
                  style="min-width: 10rem"
                />
                <Column
                  field="pay_type"
                  header="付费方式"
                  style="min-width: 8rem"
                />
                <Column
                  field="fee_amount"
                  header="权责金额"
                  style="min-width: 10rem"
                >
                  <template #body="slotProps">
                    <span>{{
                      formatCurrency(
                        slotProps.data.fee_amount,
                        slotProps.data.currency_type
                      )
                    }}</span>
                  </template>
                </Column>
                <Column
                  field="pay_amount"
                  header="已核销金额"
                  style="min-width: 10rem"
                >
                  <template #body="slotProps">
                    <span>{{
                      formatCurrency(
                        slotProps.data.pay_amount,
                        slotProps.data.currency_type
                      )
                    }}</span>
                  </template>
                </Column>
                <Column
                  field="unpay_amount"
                  header="未核销金额"
                  style="min-width: 10rem"
                >
                  <template #body="slotProps">
                    <span>{{
                      formatCurrency(
                        slotProps.data.unpay_amount,
                        slotProps.data.currency_type
                      )
                    }}</span>
                  </template>
                </Column>
                <Column
                  field="income_type"
                  header="收入类型"
                  style="min-width: 8rem"
                />
                <Column
                  field="tax_type"
                  header="税收类型"
                  style="min-width: 8rem"
                />
                <Column
                  field="customer_name"
                  header="客户名称"
                  style="min-width: 15rem"
                />
              </DataTable>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.recognition-detail-container {
  height: calc(100vh - 18rem);
  padding: 0;
}

.split-layout {
  display: flex;
  height: 100%;
  gap: 1rem;
}

.left-panel {
  flex: 0 0 15%;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.right-panel {
  flex: 0 0 85%;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.panel-header {
  background: var(--p-surface-ground);
  border-bottom: 1px solid var(--surface-border);
  padding: 0.9rem;
}

.panel-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.panel-content {
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
}

.detail-item span {
  font-size: 0.9rem;
  color: var(--text-color);
  word-break: break-all;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--p-text-color-secondary);
}

.recognition-tabs {
  height: 100%;
}

:deep(.recognition-tabs .p-tabview-panels) {
  padding: 1rem;
  height: calc(100% - 3rem);
}

/* 核销金额统计区域样式 - 苹果设计规范 */
.recognition-summary {
  background: #f8f9fa;
  border-top: 1px solid var(--surface-border);
  padding: 1.25rem;
  margin-top: auto;
}

.summary-header {
  margin-bottom: 1rem;
}

.summary-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  padding: 1rem;
  background: white;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.summary-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.summary-item label {
  font-size: 0.8125rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.amount-value {
  font-size: 1.25rem;
  font-weight: 700;
  font-variant-numeric: tabular-nums;
}

.total-amount {
  color: #007aff;
}

.current-amount {
  color: #34c759;
}

.remaining-amount {
  color: #ff9500;
}

.progress-section {
  background: white;
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.progress-label {
  font-size: 0.8125rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.progress-percentage {
  font-size: 0.875rem;
  font-weight: 700;
  color: #007aff;
  font-variant-numeric: tabular-nums;
}

.recognition-progress {
  height: 10px;
  border-radius: 3px;
  overflow: hidden;
}

:deep(.recognition-progress .p-progressbar-value) {
  background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.progress-complete :deep(.p-progressbar-value) {
  background: linear-gradient(90deg, #34c759 0%, #30d158 100%);
}

/* 认款金额输入框样式 */
.recognition-amount-input {
  width: 100%;
}

:deep(.recognition-amount-input .p-inputnumber-input) {
  font-size: 0.875rem;
  padding: 0.5rem;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--p-surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--p-text-color-secondary);
  font-size: 1.1rem;
}

/* 响应式设计 - 苹果设计规范 */
@media (max-width: 1200px) {
  .split-layout {
    flex-direction: column;
    gap: 0.75rem;
  }

  .left-panel {
    flex: 0 0 auto;
    min-height: 400px;
    max-height: 50vh;
  }

  .right-panel {
    flex: 1;
    min-height: 400px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .recognition-summary {
    margin-top: 0;
    border-top: none;
    border-radius: 0 0 12px 12px;
  }
}

@media (max-width: 768px) {
  .split-layout {
    gap: 0.5rem;
  }

  .left-panel {
    min-height: 350px;
  }

  .panel-content {
    padding: 1rem;
  }

  .recognition-summary {
    padding: 1rem;
  }

  .summary-item {
    padding: 0.75rem;
  }

  .progress-section {
    padding: 0.75rem;
  }

  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.375rem;
  }

  .amount-value {
    font-size: 1.125rem;
  }
}
</style>
