export interface FeePackageItem {
  id: number;
  package_name: string;
  remark?: string;
  create_user: string;
  created_at: string;
}

export interface FeePackageFormData
  extends Omit<FeePackageItem, "id" | "created_at" | "create_user"> {}

export interface FeePackageSimpleItem {
  id: number;
  package_name: string;
}

export interface FeeInstanceItem {
  id: number;
  fee_template_code: number;
  fee_template_name: string;
  fee: number;
  include_amount: number;
  over_fee: number;
  fee_unit: string;
  speed: string;
  priority: number;
  discount: number;
  min_fee: number;
  max_fee: number;
  remark?: string;
  created_at: string;
  is_level_fee: number;
}

export interface FeeInstanceLevelItem {
  id: number;
  fee: number;
  fee_unit: string;
  month_min: number;
  month_max: number;
  level: number;
  remark: string;
  created_at: string;
}

export interface FeeTemplateSimpleItem {
  id: number;
  fee_template_name: string;
  is_level_fee: number;
}

export interface FeeInstanceFormData {
  fee_template_id: number;
  fee: number;
  include_amount: number;
  over_fee: number;
  fee_unit: string;
  speed: string;
  priority: number;
  discount: number;
  min_fee: number;
  max_fee: number;
  remark: string;
}

export interface FeeInstanceLevelFormData {
  fee: number;
  fee_unit: string;
  month_min: number;
  month_max: number;
  level: number;
  remark: string;
}

// 是否阶梯费用映射
export const isLevelFeeMap: Record<number, string> = {
  0: "否",
  1: "是",
};

export const isLevelFeeSeverityMap: Record<number, string> = {
  0: "secondary",
  1: "success",
};
