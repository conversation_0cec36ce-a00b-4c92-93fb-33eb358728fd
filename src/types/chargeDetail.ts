// 收入权责相关类型定义

export interface ChargeDetailItem {
  id: number;
  customer_name: string;
  sale_name: string;
  sign_contract_entity: string;
  contract_legal_num: string;
  adjust_month: number;
  adjust_reason_class: string | null;
  adjust_reason: string | null;
  batch_no: string;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  order_charge_begin: string;
  order_charge_end: string;
  tax_type: string;
  fee_amount: string;
  pay_amount: string;
  unpay_amount: string;
  tax: number;
  fee_type: string;
  pay_type: string;
  currency_type: string;
  income_type: string;
  speed: string;
  charge_explain: string;
  a_info: string;
  product_main_category: string;
  product_sub_category: string;
  order_type: string;
  bill_template: string;
  contract_num: string;
  current_attr: string;
  customer_num: string;
  account_seq: string;
  income_adjust_id: number;
  charge_user: string;
  charge_type: number;
  bill_used_nums: string | null;
  created_at: string;
  updated_at: string;
}

// 收入权责搜索参数
export interface ChargeDetailParams {
  page?: number;
  pageSize?: number;
  order_no?: string;
  charge_month?: string;
  adjust_month?: string;
  [key: string]: any;
}
