export interface FeeTemplateItem {
  id: number;
  fee_template_code: number;
  fee_template_name: string;
  bill_rule: string;
  own_business: number;
  is_level_fee: number;
  state: string;
  created_at: string;
}

export interface FeeTemplateParams {
  page?: number;
  pageSize?: number;
  filter?: string;
  filterColumn?: string;
}

// 费用模板状态映射
export const feeTemplateStateSeverityMap: Record<string, string> = {
  U: "success", // 启用
  E: "danger",  // 禁用
};

export const feeTemplateStateValueMap: Record<string, string> = {
  U: "有效",
  E: "失效",
};

// 是否阶梯费用映射
export const isLevelFeeMap: Record<number, string> = {
  0: "否",
  1: "是",
};

// 归属业务映射
export const ownBusinessMap: Record<number, string> = {
  0: "普通",
  1: "语音",
  2: "流量",
};
